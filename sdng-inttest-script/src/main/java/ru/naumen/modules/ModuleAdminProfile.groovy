package ru.naumen.modules

import ru.naumen.common.shared.utils.MapProperties
import ru.naumen.metainfo.shared.adminprofile.dispatch.AddAdminProfileAction
import ru.naumen.metainfo.shared.adminprofile.dispatch.DeleteAdminProfilesAction
import ru.naumen.metainfo.shared.adminprofile.dispatch.EditAdminProfileAction

import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken

import groovy.transform.Field

@Field GSON = [
    SELF : new GsonBuilder().serializeNulls().create(),
    ACCESS_MARKER_MATRIX_MAP_TYPE : new TypeToken<Map<String, Set<String>>>(){}.getType()
]

def createProperties(def model)
{
    MapProperties properties = new MapProperties(model);

    def accessMarkerMatrix = model['accessMarkerMatrix']
    if (accessMarkerMatrix != null)
    {
        properties.setProperty('accessMarkerMatrix', GSON.SELF.fromJson(accessMarkerMatrix, GSON.ACCESS_MARKER_MATRIX_MAP_TYPE))
    }
    return properties;
}

/**
 * Создает новый профиль администрирования.
 * @param model модель профиля администрирования
 */
void addAdminProfile(Map<String, String> model)
{
    dispatch.execute(new AddAdminProfileAction(createProperties(model)))
}

/**
 * Удаляет указанный профиль администрирования.
 * @param model модель профиля администрирования
 */
void deleteAdminProfile(Map<String, String> model)
{
    deleteAdminProfile(model['code'])
}

/**
 * Удаляет профиль администрирования по его коду.
 * @param code код профиля администрирования
 */
void deleteAdminProfile(List<String> codes)
{
    dispatch.execute(new DeleteAdminProfilesAction(codes))
}

/**
 * Изменяет существующий профиль администрирования.
 * @param model модель профиля администрирования
 */
void editAdminProfile(Map<String, String> model)
{
    dispatch.execute(new EditAdminProfileAction(model['code'], createProperties(model)))
}