<?xml version="1.0" encoding="UTF-8"?>
<!--
	Файл конфигурации Log4j2 по умолчанию. Общие логеры приложения объявлены в файле log4j2-global.xml.
-->
<Configuration status="WARN">
	<Appenders>
		<Console name="Console" target="SYSTEM_OUT">
			<Log4jLayout/>
		</Console>
		<!-- Запись лога в файл с ежедневной ротацией-->
		<RollingFile name="sdng" filename="${sys:user.home}/.naumen/sd/log/sdng.log" append="true"
					 filePattern="${sys:user.home}/.naumen/sd/log/sdng.log.%d{yyyy-MM-dd}">
			<Log4jLayout/>
			<Policies>
				<!-- Ротировать раз в 1 день -->
				<TimeBasedTriggeringPolicy interval="1"/>
				<!-- Ротировать по размеру -->
				<!--<SizeBasedTriggeringPolicy size="500Mb"/> -->
			</Policies>
			<!-- Хранить не более 60 файлов-->
			<DefaultRolloverStrategy max="60"/>
		</RollingFile>
		<!-- Настройка "коротких логов" -->
		<!--	<RollingFile name="short_log" filename="${sys:user.home}/.naumen/sd/log/short.log" append="true"
						 filePattern="${sys:user.home}/.naumen/sd/log/short.log.%i%d{yyyy-MM-dd-HH-mm}">
			  <Log4jLayout />
			  <Policies>
				<TimeBasedTriggeringPolicy interval="15"/>
			  </Policies>
			</RollingFile> -->
	</Appenders>
	<Loggers>
		<!-- Scripts -->
		<logger name="ru.naumen.metainfo.shared.script.Script" level="ALL"/>

    <!-- Отключен логгер клиентских валидаций -->
    <logger name="ru.naumen.core.server.util.log.clientvalidation.ClientValidationLoggerImpl" level="INFO"/>

    <!-- Настройка rootLogger -->
    <Root level="WARN">
      <!-- Подключаем вывод в консоль -->
      <AppenderRef ref="Console"/>
      <!-- Подключаем вывод в файл -->
      <AppenderRef ref="sdng"/>
      <!-- Подключаем вывод коротких логов -->
      <!--<AppenderRef ref="short_log"/>-->
    </Root>
  </Loggers>
</Configuration>