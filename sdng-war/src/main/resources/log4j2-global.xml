<?xml version="1.0" encoding="UTF-8"?>
<!--
	Глобальные настройки логирования приложения. Применяются независимо от файла конфигурации Log4j2.
	Данные настройки могут быть переопределены во внешнем или дефолтном файле конфигурации.
 -->
<Configuration>
	<Loggers>
		<!-- Default log level for Naumen classes -->
		<logger name="ru.naumen" level="INFO"/>
		<!-- Spring -->
		<logger name="org.springframework" level="INFO"/>
		<!-- Flyway -->
		<logger name="org.flywaydb" level="INFO"/>
		<!-- Поиск авторизованного через службу пользователя в БД SD -->
		<logger name="ru.naumen.sec.server.employee" level="INFO"/>
		<!-- Общие части аутентификаторов -->
		<logger name="org.springframework.security.authentication" level="DEBUG"/>
		<!-- Логеры Kerberos/SPNEGO аутентификатора -->
		<logger name="ru.naumen.sec.server" level="INFO"/>
		<logger name="ru.naumen.sec.server.spnego" level="DEBUG"/>
		<!-- Логеры LDAP & AD аутентификаторов -->
		<logger name="org.springframework.security.ldap" level="DEBUG"/>
		<logger name="org.springframework.security.ldap.search" level="DEBUG"/>
		<logger name="ru.naumen.core.server.ldap" level="DEBUG"/>

		<!-- Логеры JWT аутентификации -->
		<logger name="ru.naumen.sec.server.jwt.filter.JwtFilter" level="INFO"/>
		<logger name="ru.naumen.sec.server.jwt.provider.JwtTokenServlet" level="INFO"/>
		<!-- User sessions -->
		<logger name="ru.naumen.sec.server.session.SessionRegistryInMemoryNauImpl" level="INFO"/>
		<logger name="ru.naumen.sec.server.session.embedded.InMemorySessionRepository" level="ERROR"/>

		<logger name="org.hibernate.cache" level="ERROR"/>
		<logger name="org.jgroups" level="WARN"/>
		<logger name="groovyx.net.http.HTTPBuilder" level="ERROR"/>
		<logger name="org.springframework.beans.factory.support" level="WARN"/>
		<logger name="ru.naumen.sec.server.saml2" level="OFF"/>

		<!-- отключение логирования проверки существования pending puts кэшей, так как фабрика сессий перегружаемая -->
		<logger name="org.infinispan.hibernate.cache.commons.access.PutFromLoadValidator" level="ERROR"/>
		<!-- отключение безвредных записей вида "Narrowing proxy" -->
		<logger name="org.hibernate.engine.internal.StatefulPersistenceContext" level="ERROR"/>
		<!-- отключение логирования исключений SQLException hibernate-ом -->
		<logger name="org.hibernate.engine.jdbc.spi.SqlExceptionHelper" level="OFF"/>
		<!-- Отключение логирования санитайзинга значений -->
		<logger name="ru.naumen.common.server.utils.html.SanitizationEventLoggerImpl" level="TRACE"/>

		<!-- Отключение логирования неудачных попыток аутентификации пользователя -->
		<logger name="ru.naumen.core.server.util.log.login.LoginFailureLogger" level="INFO"/>
	</Loggers>
</Configuration>