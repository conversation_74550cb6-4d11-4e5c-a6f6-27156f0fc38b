package ru.naumen.core.shared.filters;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;

/**
 * Контекстный фильтр по цепочке атрибутов включая вложенные объекты.
 *
 * <AUTHOR>
 * @since 29.08.2024
 */
@XmlType(name = "InContextChainFilter")
@XmlAccessorType(XmlAccessType.FIELD)
public class InContextChainWithNestedFilter extends InContextChainFilter
{
    public InContextChainWithNestedFilter()
    {
    }

    public InContextChainWithNestedFilter(String name, String rootObjectUuid, String attributeChain,
            Boolean isLinkToRelatedObjectParent, Boolean isHideRemoved, boolean ignoreIfEmpty)
    {
        super(name, rootObjectUuid, attributeChain, isLinkToRelatedObjectParent, isHideRemoved, ignoreIfEmpty);
    }
}