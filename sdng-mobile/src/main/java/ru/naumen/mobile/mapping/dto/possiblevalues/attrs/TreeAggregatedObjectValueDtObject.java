package ru.naumen.mobile.mapping.dto.possiblevalues.attrs;

import org.apache.commons.lang3.StringUtils;

import ru.naumen.mobile.mapping.dto.attrvalue.attrs.AttrValueDtObject;

/**
 * DTO для передачи объекта, являющегося частью элемента из агрегированного дерева возможных значений атрибута, на
 * сторону мобильного клиента
 *
 * @param uuid идентификатор объекта
 * @param title название объекта
 *
 * <AUTHOR>
 * @since 14.02.2025
 **/
public record TreeAggregatedObjectValueDtObject(String uuid, String title) implements AttrValueDtObject
{
    @Override
    public boolean isEmpty()
    {
        return StringUtils.isEmpty(uuid);
    }
}
