package ru.naumen.mobile.mapping.dto.form.dtos.serializers;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.INTERVAL_AVAILABLE_UNITS;
import static ru.naumen.mobile.mapping.MappingConstants.EditPresentations.*;

import java.lang.reflect.Type;
import java.util.Collection;
import java.util.List;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

import ru.naumen.core.server.script.api.recalculatevalues.restrictions.IDateTimeRestrictionContainer;
import ru.naumen.mobile.VersionConstants.Version;
import ru.naumen.mobile.context.MobileVersionContextService;
import ru.naumen.mobile.mapping.MappingConstants.EditPresentationType;
import ru.naumen.mobile.mapping.dto.form.dtos.FormAttributeEditPresentationDtObject;
import ru.naumen.mobile.services.possiblevalues.extractors.PossibleValuePresentationType;

/**
 * JSON сериализатор для преобразования {@link FormAttributeEditPresentationDtObject представления для редактирования
 * атрибута на форме} в формат ожидаемый на стороне мобильного клиента
 *
 * <AUTHOR>
 * @since 01.03.2024
 */
public class FormAttributeEditPresentationJsonSerializer implements JsonSerializer<FormAttributeEditPresentationDtObject>
{
    @Override
    @SuppressWarnings("deprecation")
    public JsonElement serialize(FormAttributeEditPresentationDtObject src, Type typeOfSrc,
            JsonSerializationContext context)
    {
        String version = MobileVersionContextService.getInvokedVersion();

        final JsonObject content = new JsonObject();
        final EditPresentationType type = src.getType();
        if (Version.FROM_V12.contains(version))
        {
            content.addProperty(TYPE, type.getCode());
            // для атрибутов поддерживающих получение возможных значений
            PossibleValuePresentationType selectType = src.getSelectType();
            if (Version.FROM_V13_2.contains(version) && selectType != null)
            {
                content.addProperty(SELECT_TYPE, selectType.getCode());
            }
        }
        else
        {
            final boolean isEditable = EditPresentationType.EDITABLE.equals(type);
            content.addProperty(EDITABLE, isEditable);
            content.addProperty(VISIBLE, isEditable || EditPresentationType.VISIBLE.equals(type));
        }

        // для атрибутов поддерживающих сканер штрих-кодов
        Boolean barcodeScannerAvailable = src.isBarcodeScannerAvailable();
        if (barcodeScannerAvailable != null)
        {
            content.addProperty(BARCODE_SCANNER_AVAILABLE, barcodeScannerAvailable);
        }

        // для типов "Дата" и "Дата/время"
        String view = src.getView();
        if (view != null)
        {
            content.addProperty(VIEW, view);
        }
        List<? extends IDateTimeRestrictionContainer> dateTimeRestriction = src.getDateTimeRestriction();
        if (dateTimeRestriction != null)
        {
            content.add(DATA_TIME_RESTRICTION, context.serialize(dateTimeRestriction));
        }

        // для типа "Временной интервал"
        Collection<String> availableIntervals = src.getIntervalAvailableUnits();
        if (availableIntervals != null)
        {
            content.add(INTERVAL_AVAILABLE_UNITS, context.serialize(availableIntervals));
        }
        String defaultInterval = src.getDefaultIntervalUnit();
        if (defaultInterval != null)
        {
            content.addProperty(DEFAULT_INTERVAL_UNIT, defaultInterval);
        }

        return content;
    }
}
