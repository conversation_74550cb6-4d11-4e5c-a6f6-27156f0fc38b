package ru.naumen.mobile.mapping.dto.tx.lists.actions;

import java.util.List;

import ru.naumen.metainfo.shared.mobile.actions.ContentListAction;
import ru.naumen.metainfo.shared.mobile.actions.ContentListActionType;

/**
 * Представление для передачи в МК {@link ContentListAction действия со списком} типа
 * {@link ContentListActionType#ADD_OBJECT "Сортировка"}
 *
 * <AUTHOR>
 * @since 05.07.2024
 */
public class SortListActionDtObject extends ListActionDtObject
{
    private List<SortableAttributeDtObject> attributes;

    public void setAttributes(
            List<SortableAttributeDtObject> attributes)
    {
        this.attributes = attributes;
    }

    public List<SortableAttributeDtObject> getAttributes()
    {
        return attributes;
    }
}
