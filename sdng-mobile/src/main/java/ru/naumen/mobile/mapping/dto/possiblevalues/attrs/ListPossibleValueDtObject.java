package ru.naumen.mobile.mapping.dto.possiblevalues.attrs;

import jakarta.annotation.Nullable;
import ru.naumen.mobile.mapping.MobileCustomTypeAdapter;
import ru.naumen.mobile.mapping.dto.possiblevalues.serializers.ListPossibleValueDtObjectJsonSerializer;

/**
 * DTO для передачи возможного значения атрибута в виде списка на сторону мобильного клиента.
 *
 * <AUTHOR>
 * @since 29.04.2022
 */
@MobileCustomTypeAdapter(ListPossibleValueDtObjectJsonSerializer.class)
public class ListPossibleValueDtObject extends AbstractListPossibleValueDtObject
{
    /**
     * Пояснение к названию возможного значения
     */
    private final String subtitle;

    public ListPossibleValueDtObject(String code, String title)
    {
        this(code, title, null);
    }

    public ListPossibleValueDtObject(String code, String title, @Nullable String subtitle)
    {
        super(code, title);
        this.subtitle = subtitle;
    }

    public String getSubtitle()
    {
        return subtitle;
    }
}
