package ru.naumen.mobile.mapping.dto.tx.lists.actions;

import ru.naumen.metainfo.shared.mobile.actions.ContentListAction;
import ru.naumen.metainfo.shared.mobile.actions.ContentListActionType;

/**
 * Представление для передачи в МК атрибутов, по которым доступна сортировка при выборе
 * {@link ContentListAction действия со списком} типа {@link ContentListActionType#ADD_OBJECT "Сортировка"}
 *
 * <AUTHOR>
 * @since 05.07.2024
 */
public record SortableAttributeDtObject(String attributeFqn, String title)
{
}
