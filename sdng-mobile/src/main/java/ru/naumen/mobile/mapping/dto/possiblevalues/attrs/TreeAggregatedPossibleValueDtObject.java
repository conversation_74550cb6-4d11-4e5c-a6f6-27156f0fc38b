package ru.naumen.mobile.mapping.dto.possiblevalues.attrs;

import java.util.List;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.mobile.mapping.MobileCustomTypeAdapter;
import ru.naumen.mobile.mapping.dto.attrvalue.attrs.AttrValueDtObject;
import ru.naumen.mobile.mapping.dto.possiblevalues.serializers.TreeAggregatedPossibleValueDtObjectJsonSerializer;

/**
 * DTO для передачи элемента из агрегированного дерева возможных значений атрибута на сторону мобильного клиента
 *
 * @param uuid идентификатор объекта
 * @param values цепочка объектов, однозначно определяющая значение
 * @param title название объекта
 * @param isLeaf флаг того, что у элемента нет дочерних
 * @param isSelectable флаг того, что данный элемент можно выбрать
 * @param children дочерние элементы
 *
 * <AUTHOR>
 * @since 01.08.2024
 */
@MobileCustomTypeAdapter(TreeAggregatedPossibleValueDtObjectJsonSerializer.class)
public record TreeAggregatedPossibleValueDtObject(
        String uuid, List<TreeAggregatedObjectValueDtObject> values, String title, boolean isLeaf, boolean isSelectable,
        List<AttrValueDtObject> children) implements AttrValueDtObject
{
    @Override
    public boolean isEmpty()
    {
        return StringUtilities.isEmpty(uuid);
    }
}
