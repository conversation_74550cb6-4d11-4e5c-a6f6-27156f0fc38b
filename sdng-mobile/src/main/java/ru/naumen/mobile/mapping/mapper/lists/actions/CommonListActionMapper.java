package ru.naumen.mobile.mapping.mapper.lists.actions;

import org.springframework.stereotype.Component;

import ru.naumen.mobile.mapping.dto.tx.lists.actions.ListActionDtObject;
import ru.naumen.mobile.services.lists.containers.actions.CommonListAction;
import ru.naumen.mobile.services.lists.containers.actions.ListAction;

/**
 * Класс, выполняющий преобразование {@link CommonListAction списочных действий без дополнительных полей} в формат МК.
 *
 * <AUTHOR>
 * @since 05.07.2024
 */
@Component
public class CommonListActionMapper implements ListActionMapper
{
    @Override
    public Class<? extends ListAction> getSupportedAction()
    {
        return CommonListAction.class;
    }

    @Override
    public ListActionDtObject map(ListAction action)
    {
        ListActionDtObject dto = new ListActionDtObject();
        ListActionMapper.fillCommonFields(action, dto);
        return dto;
    }
}
