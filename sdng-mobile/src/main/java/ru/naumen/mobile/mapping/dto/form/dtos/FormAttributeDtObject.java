package ru.naumen.mobile.mapping.dto.form.dtos;

import java.util.Collection;
import java.util.Collections;

import com.fasterxml.jackson.annotation.JsonIgnore;

import jakarta.annotation.Nullable;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.mobile.mapping.dto.attrvalue.attrs.AttrValueDtObject;

/**
 * DTO для передачи атрибута на форме на сторону мобильного клиента.
 * Используется для передачи атрибутов в ходе получения форм.
 *
 * <AUTHOR>
 * @since 13.10.2016
 */
@SuppressWarnings("java:S2160") // переопределение унаследованного equals не имеет смысла
public class FormAttributeDtObject extends BaseFormAttributeDtObject
{
    /**
     * Название атрибута
     */
    private String title;
    /**
     * Список кодов атрибутов, которые зависят от данного.
     * При изменении данного атрибута следует производить перерасчет атрибутов
     * указанных в данном списке.
     */
    private Collection<String> dependencies = Collections.emptySet();
    /**
     * Список кодов атрибутов, от которых зависит данный при расчёте ограничений на атрибуты типа "Дата" и "Дата/Время".
     * При изменении данного атрибута следует производить перерасчет атрибутов
     * указанных в данном списке.
     */
    private Collection<String> restrictionDependencies = Collections.emptySet();
    /**
     * Список кодов атрибутов, которые зависят от данного в скрипте фильтрации.
     * При изменении данного атрибута следует производить перерасчет атрибутов
     * указанных в данном списке.
     */
    private Collection<String> filtrationDependencies = Collections.emptySet();

    /**
     * Объект атрибута, используется для получения специфичных атрибутов типов
     */
    @SuppressWarnings("java:S2065")
    private final transient Attribute attribute;

    @SuppressWarnings("java:S107") // класс является DTO, нет смысла в соблюдении ограничения на количество аргументов
    public FormAttributeDtObject(
            final Attribute attribute,
            final String code,
            final String title,
            final String typeCode,
            final @Nullable AttrValueDtObject value,
            final boolean isRequired,
            final boolean onlyVisible,
            final boolean isHidden)
    {
        super(code, typeCode, value, isRequired, onlyVisible, isHidden);
        this.attribute = attribute;
        this.title = title;
    }

    @JsonIgnore
    public Attribute getAttribute()
    {
        return attribute;
    }

    public Collection<String> getDependencies()
    {
        return dependencies;
    }

    public Collection<String> getFiltrationDependencies()
    {
        return filtrationDependencies;
    }

    public Collection<String> getRestrictionDependencies()
    {
        return restrictionDependencies;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }

    public FormAttributeDtObject setDependencies(Collection<String> dependencies)
    {
        this.dependencies = dependencies;
        return this;
    }

    public FormAttributeDtObject setFiltrationDependencies(Collection<String> filtrationDependencies)
    {
        this.filtrationDependencies = filtrationDependencies;
        return this;
    }

    public void setRestrictionDependencies(Collection<String> restrictionsDependencies)
    {
        this.restrictionDependencies = restrictionsDependencies;
    }

    @Override
    public String toString()
    {
        return "FormAttributeDtObject[code=" + getCode() + ']';
    }
}
