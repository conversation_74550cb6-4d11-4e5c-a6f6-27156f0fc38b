package ru.naumen.mobile.mapping.dto.form.create;

import java.util.List;
import java.util.Set;

import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.mobile.addforms.AddForm;

/**
 * Интерфейс проводника всех типов, на которые настроена иерархия форм добавления. Собирает совокупность типов по всем
 * формам иерархии.
 *
 * <AUTHOR>
 * @since 05.08.2024
 */
public interface MobileAddFormsHierarchyCasesProvider
{
    /**
     * Возвращает типы, которые доступны хотя бы для одной формы добавления в иерархии. Если хотя бы одна форма
     * настроена на все типы, либо итоговый список типов содержит все типы класса, то возвращается пустой список.<br>
     * <b>Note:</b> сейчас имеется двойственность при обозначении списка всех типов (это либо пустой список, либо
     * список со всеми типами), данный метод исключает эту двойственность в пользу пустого списка.
     *
     * @param hierarchy иерархия форм добавления, из которой извлекаются типы
     * @return если в иерархии есть хотя бы одна форма, для которой доступны все типы, то возвращает пустой набор.
     */
    Set<ClassFqn> unionAddFormsCases(List<AddForm> hierarchy);
}
