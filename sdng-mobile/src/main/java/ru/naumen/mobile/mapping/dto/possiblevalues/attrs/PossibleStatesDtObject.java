package ru.naumen.mobile.mapping.dto.possiblevalues.attrs;

import static ru.naumen.core.shared.Constants.HasState.STATE;

import java.util.List;

import ru.naumen.mobile.mapping.MobileCustomTypeAdapter;
import ru.naumen.mobile.mapping.dto.possiblevalues.serializers.PossibleStatesDtObjectJsonSerializer;

/**
 * DTO для передачи возможных значений атрибута Статус на сторону мобильного клиента
 *
 * <AUTHOR>
 * @since 01.08.2024
 */
@MobileCustomTypeAdapter(PossibleStatesDtObjectJsonSerializer.class)
public class PossibleStatesDtObject extends PossibleValuesDtObject<ListPossibleValuesDtObject>
{
    private final ListPossibleStateDtObject currentState;

    public PossibleStatesDtObject(
            final ListPossibleStateDtObject currentState,
            final List<ListPossibleStateDtObject> values)
    {
        super(STATE, new ListPossibleValuesDtObject(values));

        this.currentState = currentState;
    }

    public ListPossibleStateDtObject getCurrentState()
    {
        return currentState;
    }
}
