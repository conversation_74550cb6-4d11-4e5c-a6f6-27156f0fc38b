package ru.naumen.mobile.mapping.dto.possiblevalues.attrs;

import java.util.Collection;

import ru.naumen.mobile.mapping.MobileCustomTypeAdapter;
import ru.naumen.mobile.mapping.dto.attrvalue.attrs.AttrValueDtObject;
import ru.naumen.mobile.mapping.dto.possiblevalues.serializers.ListPossibleValuesDtObjectJsonSerializer;

/**
 * DTO для передачи элемента из списка возможных значений атрибута на сторону мобильного клиента
 *
 * <AUTHOR>
 * @since 14.03.24
 */
@MobileCustomTypeAdapter(ListPossibleValuesDtObjectJsonSerializer.class)
public record ListPossibleValuesDtObject(
        Collection<? extends AbstractListPossibleValueDtObject> values) implements AttrValueDtObject
{
}
