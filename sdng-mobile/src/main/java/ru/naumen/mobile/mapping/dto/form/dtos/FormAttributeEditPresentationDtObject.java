package ru.naumen.mobile.mapping.dto.form.dtos;

import java.util.Collection;
import java.util.List;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.script.api.recalculatevalues.restrictions.IDateTimeRestrictionContainer;
import ru.naumen.mobile.mapping.MappingConstants.EditPresentationType;
import ru.naumen.mobile.mapping.MobileCustomTypeAdapter;
import ru.naumen.mobile.mapping.dto.form.dtos.serializers.FormAttributeEditPresentationJsonSerializer;
import ru.naumen.mobile.services.possiblevalues.extractors.PossibleValuePresentationType;

/**
 * DTO для передачи представление для редактирования атрибута на форме на сторону мобильного клиента.
 *
 * <AUTHOR>
 * @since 04.03.2024
 */
@MobileCustomTypeAdapter(FormAttributeEditPresentationJsonSerializer.class)
public class FormAttributeEditPresentationDtObject
{
    /**
     * Тип представления для редактирования: видимый (отображается как на карточке), редактируемый, заблокированный и
     * скрытый
     **/
    private EditPresentationType type;
    /** Тип представления на экране выбора значений: список, дерево */
    @Nullable
    private PossibleValuePresentationType selectType;
    /** Доступность сканера штрих-кодов */
    private Boolean barcodeScannerAvailable;
    /** Представление для отображения значений (для типов "Дата" и "Дата/время") */
    @Nullable
    private String view;
    /** Ограничения для типов Дата и Дата/время  */
    @Nullable
    private List<? extends IDateTimeRestrictionContainer> dateTimeRestriction;
    /** Доступные временные интервалы (для типа "Временной интервал") */
    @Nullable
    private Collection<String> intervalAvailableUnits;
    /** Временной интервал по умолчанию (для типа "Временной интервал") */
    @Nullable
    private String defaultIntervalUnit;

    FormAttributeEditPresentationDtObject(boolean isOnlyVisible, boolean isHidden)
    {
        this.type = getType(isOnlyVisible, isHidden);
    }

    private static EditPresentationType getType(boolean isOnlyVisible, boolean isHidden)
    {
        if (isHidden)
        {
            return EditPresentationType.HIDDEN;
        }
        return (!isOnlyVisible) ? EditPresentationType.EDITABLE : EditPresentationType.VISIBLE;
    }

    public void setType(EditPresentationType type)
    {
        this.type = type;
    }

    public void setSelectType(PossibleValuePresentationType selectType)
    {
        this.selectType = selectType;
    }

    public void setView(String view)
    {
        this.view = view;
    }

    public void setIntervalAvailableUnits(Collection<String> intervalAvailableUnits)
    {
        this.intervalAvailableUnits = intervalAvailableUnits;
    }

    public void setDefaultIntervalUnit(String defaultIntervalUnit)
    {
        this.defaultIntervalUnit = defaultIntervalUnit;
    }

    public void setBarcodeScannerAvailable(boolean barcodeScannerAvailable)
    {
        this.barcodeScannerAvailable = barcodeScannerAvailable;
    }

    public void setDateTimeRestrictions(
            List<? extends IDateTimeRestrictionContainer> dateTimeRestriction)
    {
        this.dateTimeRestriction = dateTimeRestriction;
    }

    public EditPresentationType getType()
    {
        return type;
    }

    @Nullable
    public PossibleValuePresentationType getSelectType()
    {
        return selectType;
    }

    @Nullable
    public String getView()
    {
        return view;
    }

    @Nullable
    public Collection<String> getIntervalAvailableUnits()
    {
        return intervalAvailableUnits;
    }

    @Nullable
    public String getDefaultIntervalUnit()
    {
        return defaultIntervalUnit;
    }

    public Boolean isBarcodeScannerAvailable()
    {
        return barcodeScannerAvailable;
    }

    @Nullable
    @SuppressWarnings("java:S1452")
    public List<? extends IDateTimeRestrictionContainer> getDateTimeRestriction()
    {
        return dateTimeRestriction;
    }
}
