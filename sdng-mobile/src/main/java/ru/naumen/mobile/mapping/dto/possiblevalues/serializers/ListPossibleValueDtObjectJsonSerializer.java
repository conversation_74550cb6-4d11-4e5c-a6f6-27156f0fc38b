package ru.naumen.mobile.mapping.dto.possiblevalues.serializers;

import static ru.naumen.mobile.VersionConstants.Version.FROM_V15;
import static ru.naumen.mobile.mapping.MappingConstants.Common.CODE;
import static ru.naumen.mobile.mapping.MappingConstants.Common.UUID;
import static ru.naumen.mobile.mapping.MappingConstants.PossibleValues.SUBTITLE;
import static ru.naumen.mobile.mapping.MappingConstants.PossibleValues.TITLE;

import java.lang.reflect.Type;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

import ru.naumen.mobile.context.MobileVersionContextService;
import ru.naumen.mobile.mapping.dto.possiblevalues.attrs.ListPossibleValueDtObject;

/**
 * JSON сериализатор для преобразования {@link ListPossibleValueDtObject элемента списка возможных значений атрибута}
 * в формат ожидаемый на стороне мобильного клиента
 *
 * <AUTHOR>
 * @since 29.04.2022
 */
public class ListPossibleValueDtObjectJsonSerializer implements JsonSerializer<ListPossibleValueDtObject>
{
    @Override
    public JsonElement serialize(ListPossibleValueDtObject src, Type typeOfSrc, JsonSerializationContext context)
    {
        final JsonObject object = new JsonObject();
        if (FROM_V15.contains(MobileVersionContextService.getInvokedVersion()))
        {
            object.add(UUID, new JsonPrimitive(src.getUuid()));
        }
        else
        {
            object.add(CODE, new JsonPrimitive(src.getUuid()));
        }
        object.add(TITLE, new JsonPrimitive(src.getTitle()));
        final String subtitle = src.getSubtitle();
        if (subtitle != null)
        {
            object.add(SUBTITLE, new JsonPrimitive(subtitle));
        }
        return object;
    }
}
