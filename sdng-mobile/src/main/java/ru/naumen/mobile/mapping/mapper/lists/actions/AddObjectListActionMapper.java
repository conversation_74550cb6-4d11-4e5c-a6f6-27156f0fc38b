package ru.naumen.mobile.mapping.mapper.lists.actions;

import org.springframework.stereotype.Component;

import ru.naumen.mobile.mapping.dto.form.MobileFormExecutionContext;
import ru.naumen.mobile.mapping.dto.tx.lists.actions.AddObjectListActionDtObject;
import ru.naumen.mobile.mapping.dto.tx.lists.actions.ListActionDtObject;
import ru.naumen.mobile.services.lists.containers.actions.AddObjectListAction;
import ru.naumen.mobile.services.lists.containers.actions.ListAction;

/**
 * Класс, выполняющий преобразование {@link AddObjectListAction списочных действий типа "Добавить объект"} в формат МК.
 *
 * <AUTHOR>
 * @since 05.07.2024
 */
@Component
public class AddObjectListActionMapper implements ListActionMapper
{
    @Override
    public Class<? extends ListAction> getSupportedAction()
    {
        return AddObjectListAction.class;
    }

    @Override
    public ListActionDtObject map(ListAction action)
    {
        AddObjectListActionDtObject dto = new AddObjectListActionDtObject();
        ListActionMapper.fillCommonFields(action, dto);

        AddObjectListAction addObjectListAction = (AddObjectListAction)action;
        dto.setFormCode(addObjectListAction.getFormCode());
        dto.setContext(new MobileFormExecutionContext().setPossibleCases(addObjectListAction.getPossibleCases()));
        dto.setAttributes(addObjectListAction.getAttributes());
        return dto;
    }
}
