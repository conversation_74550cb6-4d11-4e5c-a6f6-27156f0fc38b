package ru.naumen.mobile.metainfoadmin.client.lists;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import jakarta.inject.Inject;

import com.google.common.collect.Collections2;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.DefaultPropertyFormDisplayImpl;
import ru.naumen.core.shared.Constants.PushMobile;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.mobile.lists.MobileList;
import ru.naumen.metainfo.shared.mobile.lists.MobileRelObjectsList;
import ru.naumen.metainfoadmin.client.tags.property.TagsProperty;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;
import ru.naumen.mobile.metainfoadmin.shared.InterfaceInfo;
import ru.naumen.mobile.metainfoadmin.shared.lists.MobileListDto;

/**
 * Презентер формы редактирования списка объектов в мобильном приложении
 *
 * <AUTHOR>
 * @since 13 мая 2015 г.
 */
public class EditMobileListFormPresenter extends MobileListFormPresenter<MobileListContext>
{
    @Inject
    public EditMobileListFormPresenter(DefaultPropertyFormDisplayImpl display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void bindCaseListProperty()
    {
        final MobileList list = context.getContent();
        boolean isPushMobileClass = PushMobile.FQN.isSameClass(list.getFqnOfClass());
        if (isPushMobileClass)
        {
            return;
        }
        super.bindCaseListProperty();
    }

    @Override
    protected void bindSpecialProperties()
    {
        super.bindSpecialProperties();
        final MobileList list = context.getContent();
        allowActionsWithObjectsProperty.setValue(list.isAllowActionsWithObjects());
        attributeSelectProperty.setValue(getAttrTreeObject());

        titleProperty.setValue(metainfoUtils.getLocalizedValue(list.getCaption()));
        classProperty.trySetObjValue(list.getFqnOfClass().toString());
        if (list instanceof MobileRelObjectsList)
        {
            classProperty.setDisable();
        }

        codeProperty.setValue(list.getCode());
        codeProperty.setDisable();
        validation.unvalidate(codeProperty);
        caseListProperty.trySetObjValue(Collections2.transform(list.getCases(), ClassFqn::toString));
        profilesProperty.trySetObjValue(list.getProfiles());
    }

    @Override
    protected String getInitialSettingsSeValue()
    {
        final MobileList list = context.getContent();
        return list.getSettingsSet();
    }

    @Override
    protected void bindWithLinkToUserProperty()
    {
        final MobileList list = context.getContent();
        boolean isPushMobileClass = PushMobile.FQN.isSameClass(list.getFqnOfClass());
        super.bindWithLinkToUserProperty();
        if (isPushMobileClass)
        {
            withLinkToUser.setValue(true);
        }
        withLinkToUser.setEnabled(!isPushMobileClass);
    }

    @Override
    protected void doAction(String title, ClassFqn fqnOfClass, Collection<ClassFqn> cases, Collection<String> profiles,
            Collection<String> tags, RelationsAttrTreeObject attr, final AsyncCallback<Void> callback,
            boolean allowActionsWithObjects, String settingsSetVal)
    {
        mobileSettingsService.editMobileList(context.getContent(), title, fqnOfClass, cases, profiles, tags,
                tagsProperty.getPendingTags(), attr, new BasicCallback<MobileListDto>()
                {
                    @Override
                    public void handleFailure(Throwable t)
                    {
                        callback.onFailure(t);
                    }

                    @Override
                    public void handleSuccess(MobileListDto dto)
                    {
                        ClassFqn oldClassFqn = context.getContent().getFqnOfClass();
                        ClassFqn newClassFqn = dto.getMobileContent().getFqnOfClass();
                        context.setDto(dto);
                        if (!oldClassFqn.equals(newClassFqn))
                        {
                            onClassChanged(callback);
                            return;
                        }
                        callback.onSuccess(null);
                    }
                }, allowActionsWithObjects, settingsSetVal);
    }

    @Override
    protected String getCaption()
    {
        return messages.listEdit();
    }

    @Override
    protected void loadCases(ReadyState readyState)
    {
        loadCases(context.getContent().getFqnOfClass().toString(), new BasicCallback<>(readyState));
    }

    @Override
    protected void setTagsPropertyValue(TagsProperty tags)
    {
        tags.trySetObjValue(context.getContent().getTags());
    }

    @Override
    protected boolean validateUniqueCode()
    {
        return true;
    }

    @Override
    void bindSelectAttributeProperty()
    {
        super.bindSelectAttributeProperty();
        final MobileList list = context.getContent();
        if (list instanceof MobileRelObjectsList)
        {
            withLinkToUser.setValue(true);
            selectAttributePR = getDisplay().addPropertyAfter(attributeSelectProperty, withLinkToUserPR);
            validation.validate(attributeSelectProperty, atoNotNullValidator);
        }
    }

    private RelationsAttrTreeObject getAttrTreeObject()
    {
        MobileList list = context.getContent();
        if (!(list instanceof MobileRelObjectsList))
        {
            return null;
        }
        List<AttrReference> attrChain = ((MobileRelObjectsList)list).getAttrsChain();
        if (CollectionUtils.isEmpty(attrChain))
        {
            return null;
        }

        InterfaceInfo info = context.getInterfaceInfo();
        Map<String, Attribute> attributes = info.getCodeToAttribute();

        RelationsAttrTreeObject result = null;
        for (AttrReference attr : attrChain)
        {
            // После задачи NSDPRD-5243 выводимый атрибут может отсутствовать в системе.
            final Attribute attribute = attributes.get(attr.getAttrCode());
            if (attribute == null)
            {
                break;
            }
            result = new RelationsAttrTreeObject(result, attribute);
        }
        return result;
    }

    /**
     * В случае если в процессе редактирования был изменен класс объектов списка,
     * обновляем параменты контекста, зависящие от класса
     */
    private void onClassChanged(final AsyncCallback<Void> callback)
    {
        context.resetFakeList();
        metainfoService.getMetaClass(context.getContent().getFqnOfClass(), new BasicCallback<MetaClass>()
        {
            @Override
            public void handleSuccess(MetaClass metaClass)
            {
                context.setMetaInfo(metaClass);

                metainfoService.getAllAttributes(metaClass.getFqn(), new BasicCallback<Collection<Attribute>>()
                {
                    @Override
                    public void handleSuccess(Collection<Attribute> attributes)
                    {
                        context.setAttributes(attributes);
                        callback.onSuccess(null);
                    }
                });
            }
        });
    }
}
