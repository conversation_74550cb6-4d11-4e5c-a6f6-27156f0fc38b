package ru.naumen.mobile.metainfoadmin.shared.lists;

import java.util.List;

import net.customware.gwt.dispatch.shared.Action;
import net.customware.gwt.dispatch.shared.Result;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Абстрактный action добавления/редактирования списка объектов в мобильном приложении
 *
 * <AUTHOR>
 * @since 28 мая 2015 г.
 * @param <T>
 */
public abstract class AbstractMobileListAction<T extends Result> implements Action<T>
{
    private String title;
    private ClassFqn clazz;
    private List<ClassFqn> cases;
    private List<String> profiles;
    private List<String> tags;
    private String settingsSet;
    private List<AttrReference> attrChain;
    private boolean allowActionsWithObjects;

    public AbstractMobileListAction()
    {
    }

    @SuppressWarnings("java:S107")
    protected AbstractMobileListAction(String title, ClassFqn clazz, List<ClassFqn> cases, List<String> profiles,
            List<String> tags, List<AttrReference> attrChain, boolean allowActionsWithObjects,
            String settingsSet)
    {
        this.title = title;
        this.clazz = clazz;
        this.cases = cases;
        this.profiles = profiles;
        this.tags = tags;
        this.attrChain = attrChain;
        this.allowActionsWithObjects = allowActionsWithObjects;
        this.settingsSet = settingsSet;
    }

    public List<AttrReference> getAttrChain()
    {
        return attrChain;
    }

    public List<ClassFqn> getCases()
    {
        return cases;
    }

    public ClassFqn getClazz()
    {
        return clazz;
    }

    public List<String> getProfiles()
    {
        return profiles;
    }

    public List<String> getTags()
    {
        return tags;
    }

    public String getTitle()
    {
        return title;
    }

    public boolean isAllowActionsWithObjects()
    {
        return allowActionsWithObjects;
    }

    public String getSettingsSet()
    {
        return settingsSet;
    }
}
