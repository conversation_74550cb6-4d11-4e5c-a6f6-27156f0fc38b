package ru.naumen.mobile.metainfoadmin.server.lists;

import static ru.naumen.core.shared.permission.PermissionType.CREATE;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.tags.usage.listeners.mobile.view.events.AfterAddMobileViewEvent;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.utils.ILocaleInfo;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.mobile.MobileSettings;
import ru.naumen.metainfo.shared.mobile.lists.MobileList;
import ru.naumen.metainfo.shared.mobile.lists.MobileObjectList;
import ru.naumen.metainfo.shared.mobile.lists.MobileRelObjectsList;
import ru.naumen.metainfo.shared.ui.ListFilter;
import ru.naumen.metainfo.shared.ui.ListFilterAndElement;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement.ConditionCode;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement.PropertyCode;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.mobile.metainfo.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.server.MobileContentsValidator;
import ru.naumen.mobile.metainfoadmin.server.log.MobileSettingsLogService;
import ru.naumen.mobile.metainfoadmin.server.mappers.MobileListSortMapper;
import ru.naumen.mobile.metainfoadmin.shared.ContentUuidResponse;
import ru.naumen.mobile.metainfoadmin.shared.lists.AddMobileListAction;

/**
 *
 * <AUTHOR>
 * @since 24 апр. 2015 г.
 */
@Component
public class AddMobileListActionHandler extends TransactionalActionHandler<AddMobileListAction, ContentUuidResponse>
{
    private final MobileSettingsService mobileSettingsService;
    private final ILocaleInfo localeInfo;
    private final MobileContentsValidator mobileContentsValidator;
    private final MobileSettingsLogService mobileSettingsLogService;
    private final ApplicationEventPublisher eventPublisher;
    private final MobileListSortMapper listSortMapper;
    private final MetainfoModification metainfoModification;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public AddMobileListActionHandler(MobileSettingsService mobileSettingsService,
            ILocaleInfo localeInfo,
            MobileContentsValidator mobileContentsValidator,
            MobileSettingsLogService mobileSettingsLogService,
            ApplicationEventPublisher eventPublisher,
            MobileListSortMapper listSortMapper,
            MetainfoModification metainfoModification,
            AdminPermissionCheckService adminPermissionCheckService)
    {
        this.mobileSettingsService = mobileSettingsService;
        this.localeInfo = localeInfo;
        this.mobileContentsValidator = mobileContentsValidator;
        this.mobileSettingsLogService = mobileSettingsLogService;
        this.eventPublisher = eventPublisher;
        this.listSortMapper = listSortMapper;
        this.metainfoModification = metainfoModification;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    @Override
    public ContentUuidResponse executeInTransaction(AddMobileListAction action, ExecutionContext context)
            throws DispatchException
    {
        String title = action.getTitle();
        ClassFqn clazz = action.getClazz();
        List<ClassFqn> cases = action.getCases();
        Collection<String> profiles = action.getProfiles();
        List<AttrReference> attrChain = action.getAttrChain();

        MobileList list;

        if (!CollectionUtils.isEmpty(attrChain))
        {
            list = new MobileRelObjectsList();
            ((MobileRelObjectsList)list).setAttrChain(attrChain);
        }
        else
        {
            list = new MobileObjectList();
        }

        list.setCaption(Lists.newArrayList(new LocalizedString(localeInfo.getCurrentLang(), title)));
        list.setSettingsSet(action.getSettingsSet());
        adminPermissionCheckService.checkPermission(list, CREATE);

        if (CollectionUtils.isEmpty(cases))
        {
            list.setClazz(clazz);
        }
        list.setCases(cases);
        list.setProfiles(profiles);
        list.setTags(action.getTags());
        list.setUuid(action.getCode());
        list.setAttributes(action.getListAttributes());
        list.setAllowActionsWithObjects(action.isAllowActionsWithObjects());
        Optional.ofNullable(action.getListSort()).map(listSortMapper::transform).ifPresent(
                list::setAttrOrders);
        if (action.getListFilter() != null)
        {
            ListFilter filter = action.getListFilter();

            if (action.getOriginalListTitle() == null)
            {
                filter.getElements().add(createArchiveListFilter());
            }
            list.setListFilter(filter);
        }
        else if (action.getOriginalListTitle() == null)
        {
            ListFilter archiveFilter = new ListFilter();
            archiveFilter.setElements(List.of(createArchiveListFilter()));
            list.setListFilter(archiveFilter);
        }

        mobileContentsValidator.validateList(list);

        MobileSettings mobileSettings = mobileSettingsService.getSettingsOrEmpty();
        List lists = mobileSettings.getLists();
        lists.add(list);

        metainfoModification.modify(
                MetainfoModification.MetainfoRegion.MOBILE_SETTINGS);
        if (mobileSettingsService.importSettings(mobileSettings))
        {
            if (action.getOriginalListTitle() != null)
            {
                mobileSettingsLogService.logCopyView(list, action.getOriginalListTitle());
            }
            else
            {
                mobileSettingsLogService.logAddView(list, "");
            }
        }

        eventPublisher.publishEvent(new AfterAddMobileViewEvent(list));

        return new ContentUuidResponse(list.getUuid());
    }

    /**
     * Создать элемент фильтра по признаку: "Признак архивирования" - "нет"
     * @return элемент фильтра
     */
    private static ListFilterAndElement createArchiveListFilter()
    {
        ListFilterOrElement<Boolean> listFilterOrElement = new ListFilterOrElement<>();
        listFilterOrElement.setProperty(PropertyCode.IS_WITH_SEMANTIC_ALLOWED, Boolean.TRUE.toString());
        listFilterOrElement.setProperty(PropertyCode.IS_ATTRIBUTE_OF_RELATED_OBJECT, Boolean.FALSE.toString());
        listFilterOrElement.setProperty(PropertyCode.IS_LINK_TO_PARENT, Boolean.FALSE.toString());
        listFilterOrElement.setProperty(PropertyCode.ATTR_TYPE_CODE, PropertyCode.BOOL_ATTR_TYPE);
        listFilterOrElement.setProperty(PropertyCode.CONDITION_CODE, ConditionCode.CONTAINS);
        listFilterOrElement.setProperty(PropertyCode.ATTRIBUTE_FQN, AbstractBO.REMOVED_ABSTRACT_BO);
        listFilterOrElement.setValue(false);

        ListFilterAndElement listFilterAndElement = new ListFilterAndElement();
        listFilterAndElement.getElements().add(listFilterOrElement);
        return listFilterAndElement;
    }
}
