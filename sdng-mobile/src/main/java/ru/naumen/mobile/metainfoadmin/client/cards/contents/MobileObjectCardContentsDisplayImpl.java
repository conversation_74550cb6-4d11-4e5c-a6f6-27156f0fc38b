package ru.naumen.mobile.metainfoadmin.client.cards.contents;

import jakarta.inject.Inject;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTML;

import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolBarDisplayImpl;
import ru.naumen.core.client.mvp.Display;

/**
 * Дисплей контентов для карточки объекта в мобильном приложении
 * <AUTHOR>
 * @since 20 февр. 2019 г.
 *
 */
public class MobileObjectCardContentsDisplayImpl extends Composite implements Display
{
    interface MobileObjectCardContentsDisplayImplUiBinder extends
            UiBinder<FlowPanel, MobileObjectCardContentsDisplayImpl>
    {
    }

    private static MobileObjectCardContentsDisplayImplUiBinder uiBinder = GWT.create(
            MobileObjectCardContentsDisplayImplUiBinder.class);

    @UiField
    HTML cardTitle;
    @UiField
    ButtonToolBarDisplayImpl toolBar;
    @UiField
    FlowPanel cardPanel;
    @UiField
    FlowPanel outer;
    @UiField
    FlowPanel contentsContainer;
    @UiField
    FlowPanel commandPanel;

    @Inject
    public MobileObjectCardContentsDisplayImpl()
    {
        initWidget(uiBinder.createAndBindUi(this));
    }

    @Override
    public void destroy()
    {
        //Нечего очищать
    }

    public FlowPanel getCardPanel()
    {
        return cardPanel;
    }

    public HTML getCardTitle()
    {
        return cardTitle;
    }

    public FlowPanel getCommandPanel()
    {
        return commandPanel;
    }

    public FlowPanel getContentsContainer()
    {
        return contentsContainer;
    }

    public FlowPanel getOuter()
    {
        return outer;
    }

    public ButtonToolBarDisplayImpl getToolBar()
    {
        return toolBar;
    }

    @Override
    public void startProcessing()
    {
        //Нет необходимости в блокировке
    }

    @Override
    public void stopProcessing()
    {
        //Нет необходимости в блокировке
    }
}
