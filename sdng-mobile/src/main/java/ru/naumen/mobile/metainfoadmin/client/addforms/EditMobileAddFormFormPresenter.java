package ru.naumen.mobile.metainfoadmin.client.addforms;

import jakarta.inject.Inject;

import com.google.common.collect.Collections2;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.DefaultPropertyFormDisplayImpl;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.mobile.addforms.AddForm;
import ru.naumen.metainfoadmin.client.tags.property.TagsProperty;
import ru.naumen.mobile.metainfoadmin.shared.addforms.MobileAddFormDto;

/**
 * Презентер формы добавления объекта мобильного приложения
 *
 * <AUTHOR>
 * @since 03.02.2017
 */
public class EditMobileAddFormFormPresenter extends MobileAddFormFormPresenter<MobileAddFormContext>
{
    @Inject
    private MetainfoUtils utils;

    @Inject
    public EditMobileAddFormFormPresenter(DefaultPropertyFormDisplayImpl display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void refreshDisplay()
    {
        final AddForm form = context.getContent();
        titleProperty.setValue(utils.getLocalizedValue(form.getCaption()));

        codeProperty.setValue(form.getCode());
        codeProperty.setDisable();
        isTransferDeviceGeoPosition.setValue(form.isTransferDeviceGeoPosition());

        classProperty.trySetObjValue(form.getFqnOfClass().toString());
        classProperty.setDisable();
    }

    @Override
    protected void bindCaseListProperty()
    {
        if (Constants.PushMobile.CLASS_ID.equals(SelectListPropertyValueExtractor.getValue(classProperty)))
        {
            return;
        }
        super.bindCaseListProperty();
    }

    @Override
    protected void bindSpecialProperties()
    {
        super.bindSpecialProperties();
        final AddForm form = context.getContent();
        titleProperty.setValue(utils.getLocalizedValue(form.getCaption()));

        codeProperty.setValue(form.getCode());
        codeProperty.setDisable();
        isTransferDeviceGeoPosition.setValue(form.isTransferDeviceGeoPosition());

        classProperty.trySetObjValue(form.getFqnOfClass().toString());
        classProperty.setDisable();

        String parentUuid = null == form.getParent() ? null : form.getParent().getUuid();
        parentFormProperty.trySetObjValue(parentUuid);
        parentFormProperty.setDisable();

        if (!Constants.PushMobile.CLASS_ID.equals(SelectListPropertyValueExtractor.getValue(classProperty)))
        {
            caseListProperty.trySetObjValue(Collections2.transform(form.getCases(), ClassFqn.TO_STRING_CONVERTER));
        }
        profilesProperty.trySetObjValue(form.getProfiles());
    }

    @Override
    protected String getInitialSettingsSeValue()
    {
        final AddForm form = context.getContent();
        return form.getSettingsSet();
    }

    @Override
    protected AddForm createAddForm()
    {
        return new AddForm(context.getContent());
    }

    @Override
    protected void doAction(String title, AddForm form, final AsyncCallback<Void> callback)
    {
        form.setVoiceCreationInherited(context.getContent().getVoiceCreationInherited());
        form.setAttrsOnFormInherited(context.getContent().getAttrsOnFormInherited());
        mobileSettingsService.editAddFrom(context.getContent().getUuid(), title, form, tagsProperty.getPendingTags(),
                new BasicCallback<MobileAddFormDto>()
                {
                    @Override
                    public void handleFailure(Throwable t)
                    {
                        callback.onFailure(t);
                    }

                    @Override
                    public void handleSuccess(MobileAddFormDto dto)
                    {
                        context.setDto(dto);
                        callback.onSuccess(null);
                    }
                });
    }

    @Override
    protected String getCaption()
    {
        return messages.addFormEdit();
    }

    @Override
    protected void loadCases(ReadyState readyState)
    {
        loadCases(context.getContent().getFqnOfClass().toString(), new BasicCallback<>(readyState));
    }

    @Override
    protected void loadProfiles(ReadyState readyState)
    {
        loadProfiles(context.getContent().getFqnOfClass(), new BasicCallback<>(readyState));
    }

    @Override
    protected void setTagsPropertyValue(TagsProperty tags)
    {
        tags.trySetObjValue(context.getContent().getTags());
    }

    @Override
    protected boolean validateUniqueCode()
    {
        return true;
    }

    @Override
    protected void fillParentAddForms()
    {
        if (context == null)
        {
            return;
        }
        SingleSelectCellList<?> valueWidget = parentFormProperty.<SingleSelectCellList<?>> getValueWidget();
        AddForm form = context.getContent();
        if (form.getParent() == null)
        {
            valueWidget.addItem("", "");
        }
        else
        {
            valueWidget.addItem(metainfoUtils.getLocalizedValue(form.getParent().getCaption()),
                    form.getParent().getCode());
        }
    }
}
