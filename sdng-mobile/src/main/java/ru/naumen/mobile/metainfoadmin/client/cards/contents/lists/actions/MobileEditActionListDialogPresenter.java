package ru.naumen.mobile.metainfoadmin.client.cards.contents.lists.actions;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.client.forms.DialogDisplay.DialogWidth;
import ru.naumen.core.client.forms.FormDisplay;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.metainfo.shared.mobile.contents.AbstractMobileContent;
import ru.naumen.metainfo.shared.mobile.contents.MobileListContentBase;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.EditableToolPanelMessages;
import ru.naumen.mobile.metainfoadmin.client.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.client.cards.MobileObjectCardContext;

/**
 * Презентер диалогового окна формы "настройки действий" в контентах "Список связанных объектов" и "Список вложенных
 * объектов"
 *
 * <AUTHOR>
 * @since 08.05.2024
 */
public class MobileEditActionListDialogPresenter extends OkCancelPresenter<FormDisplay>
        implements CallbackPresenter<MobileObjectCardContext, AbstractMobileContent>
{
    private final MobileEditActionListPresenter editActionListPresenter;
    private final MobileSettingsService mobileSettingsService;
    private final EditableToolPanelMessages messages;

    private AsyncCallback<AbstractMobileContent> callback;
    private MobileListContentBase listContent;
    private PresenterRegistration editActionListPR;
    private MobileObjectCardContext context;

    @Inject
    public MobileEditActionListDialogPresenter(FormDisplay display, EventBus eventBus,
            MobileEditActionListPresenter editActionListPresenter, MobileSettingsService mobileSettingsService,
            EditableToolPanelMessages messages)
    {
        super(display, eventBus);
        this.editActionListPresenter = editActionListPresenter;
        this.mobileSettingsService = mobileSettingsService;
        this.messages = messages;
    }

    @Override
    public void init(@Nullable MobileObjectCardContext context, AsyncCallback<AbstractMobileContent> callback)
    {
        this.context = context;
        this.callback = callback;
        editActionListPresenter.init(context, listContent);
        getDisplay().addContent(editActionListPresenter.getDisplay());
    }

    /**
     * Инициализирует форму действий в контенте данными из контекста настроек карточки объекта в МК
     *
     * @param context контекст настроек карточки объекта в МК
     * @param content данные контента на карточки объекта в МК
     * @param callback метод обратного вызова, который будет вызван при завершении работы формы
     */
    public void init(@Nullable MobileObjectCardContext context, AbstractMobileContent content,
            AsyncCallback<AbstractMobileContent> callback)
    {
        if (content instanceof MobileListContentBase mobileListContentBase)
        {
            this.listContent = mobileListContentBase;
        }
        init(context, callback);
    }

    @Override
    public void onApply()
    {
        ObjectCard objectCard = context.getView();
        editActionListPresenter.onApply();
        mobileSettingsService.editMobileContent(objectCard.getUuid(), listContent,
                new BasicCallback<AbstractMobileContent>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(AbstractMobileContent list)
                    {
                        callback.onSuccess(list);
                        MobileEditActionListDialogPresenter.super.onApply();
                        unbind();
                    }
                });
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(messages.editToolPanel());
        editActionListPR = registerChildPresenter(editActionListPresenter);
        getDisplay().setDialogWidth(DialogWidth.W920);
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        if (editActionListPR != null)
        {
            editActionListPR.unregister();
            editActionListPR = null;
        }
    }
}
