package ru.naumen.mobile.metainfoadmin.client.addforms;

import static ru.naumen.metainfo.shared.filters.MetaClassFilters.isSomeClass;
import static ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormPropertyCodes.IS_GEO_REQUIRED;

import java.util.Collection;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.common.base.Predicate;
import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.widgets.DefaultPropertyFormDisplayImpl;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.mobile.addforms.AddForm;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.EditableToolPanelMessages;
import ru.naumen.mobile.metainfoadmin.client.MobileContentFormPresenter;

/**
 * Абстрактный презентер формы добавления/редактирования формы добавления объекта мобильного приложения
 *
 * <AUTHOR>
 * @since 03.02.2017
 */
public abstract class MobileAddFormFormPresenter<C> extends MobileContentFormPresenter<C>
{
    @Named(PropertiesGinModule.CHECK_BOX)
    @Inject
    Property<Boolean> isTransferDeviceGeoPosition;
    @Inject
    private NotEmptyValidator notEmptyValidator;
    @Inject
    private EditableToolPanelMessages editableToolPanelMessages;

    MobileAddFormFormPresenter(DefaultPropertyFormDisplayImpl display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void bindProperties()
    {
        titleProperty.setMaxLength(Constants.MAX_MOBILE_TITLE_LENGTH);
        titleProperty.setCaption(messages.title());
        titleProperty.setValidationMarker(true);
        validation.validate(titleProperty, notEmptyValidator);
        titleProperty.addValueChangeHandler(titleVCH);
        titlePR = getDisplay().add(titleProperty);
        super.bindProperties();
    }

    @Override
    protected void bindSpecialProperties()
    {
        super.bindSpecialProperties();
        bindTransferDeviceGeoPositionProperty();
    }

    @Override
    protected Predicate<MetaClassLite> buildMetaClassPredicate()
    {
        return isSomeClass(Constants.Root.FQN);
    }

    protected abstract AddForm createAddForm();

    @Override//сохр. формы добавления (с параметром родитель)
    protected void doAction(String parentFormUuid, ClassFqn fqnOfClass, Collection<ClassFqn> cases,
            Collection<String> profiles,
            Collection<String> tags, String settingsSet, AsyncCallback<Void> callback)
    {
        AddForm form = createAddForm();

        String title = titleProperty.getValue();
        form.setUuid(codeProperty.getValue());
        if (CollectionUtils.isEmpty(cases))
        {
            form.setClazz(fqnOfClass);
        }
        form.setCases(Lists.newArrayList(cases));
        if (StringUtilities.isNotEmpty(parentFormUuid))
        {
            AddForm parentAddForm = new AddForm();
            parentAddForm.setUuid(parentFormUuid);
            form.setParent(parentAddForm);
            form.setAttrsOnFormInherited(true);
            form.setVoiceCreationInherited(true);
        }
        else
        {
            form.setParent(null);
            form.setAttrsOnFormInherited(false);
            form.setVoiceCreationInherited(false);
        }
        form.setProfiles(profiles);
        form.setTags(tags);
        form.setTransferDeviceGeoPosition(isTransferDeviceGeoPosition.getValue());
        form.setSettingsSet(settingsSet);

        doAction(title, form, callback);
    }

    abstract protected void doAction(String title, AddForm form, AsyncCallback<Void> callback);

    @Override
    protected void ensureDebugIds()
    {
        super.ensureDebugIds();
        DebugIdBuilder.ensureDebugId(titleProperty, "title");
        DebugIdBuilder.ensureDebugId(isTransferDeviceGeoPosition, IS_GEO_REQUIRED);
    }

    @Override
    protected String getProfilesPropertyCaption()
    {
        return messages.cardAvailableToProfiles();
    }

    private void bindTransferDeviceGeoPositionProperty()
    {
        isTransferDeviceGeoPosition.setCaption(editableToolPanelMessages.transferDeviceGeostation());
        getDisplay().add(isTransferDeviceGeoPosition);
    }

    @Override
    protected void bindParentFormProperty()
    {
        parentFormProperty = classPropertyProvider.get();
        parentFormProperty.setCaption(messages.parentAddForm());
        parentFormPropertyPR = getDisplay().addPropertyAfter(parentFormProperty, caseListPropertyPR);
        DebugIdBuilder.ensureDebugId(parentFormProperty, "parentAddForm");
        fillParentAddForms();
    }

    abstract protected void fillParentAddForms();

    @Override
    protected void insertProfilesPropertyPR()
    {
        profilesPropertyPR = getDisplay().addPropertyAfter(profilesProperty, parentFormPropertyPR);
    }

    @Override
    protected String getParentFormValue()
    {
        if (parentFormProperty == null || SelectListPropertyValueExtractor.getValue(parentFormProperty) == null)
        {
            return null;
        }
        return SelectListPropertyValueExtractor.getValue(parentFormProperty).toString();
    }

}
