package ru.naumen.dynamicfield.core.server.attribute;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

/**
 * Реализация реестра фабрик для создания описаний типов динамических полей.
 * <AUTHOR>
 * @since Jul 11, 2024
 */
@Component
public class DynamicAttributeTypeFactoryRegistryImpl implements DynamicAttributeTypeFactoryRegistry
{
    private final Map<String, DynamicAttributeTypeFactory> typeFactories = new HashMap<>();

    @Inject
    public DynamicAttributeTypeFactoryRegistryImpl(List<DynamicAttributeTypeFactory> typeFactories)
    {
        typeFactories.forEach(factory -> this.typeFactories.put(factory.getTypeCode(), factory));
    }

    @Nullable
    @Override
    public DynamicAttributeTypeFactory getFactory(String typeCode)
    {
        return typeFactories.get(typeCode);
    }

    @Override
    public boolean hasFactory(String typeCode)
    {
        return typeFactories.containsKey(typeCode);
    }
}
