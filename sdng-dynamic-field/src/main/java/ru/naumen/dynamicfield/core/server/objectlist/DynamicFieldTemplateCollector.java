package ru.naumen.dynamicfield.core.server.objectlist;

import java.util.Set;

import ru.naumen.dynamicfield.core.server.dao.TemplatePath;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.objectlist.shared.ObjectListDataContext;

/**
 * Сборщик шаблонов динамических полей, используемых среди объектов списка.
 * <AUTHOR>
 * @since Jul 20, 2024
 */
@FunctionalInterface
public interface DynamicFieldTemplateCollector
{
    /**
     * Собирает пути к шаблонам, используемым и доступным в списке объектов.
     * @param jsonAttribute атрибут типа «Значения динамических полей»
     * @param dataContext контекст получения списка объектов
     * @return множество идентификатором шаблонов
     */
    Set<TemplatePath> collectTemplates(Attribute jsonAttribute, ObjectListDataContext dataContext);
}
