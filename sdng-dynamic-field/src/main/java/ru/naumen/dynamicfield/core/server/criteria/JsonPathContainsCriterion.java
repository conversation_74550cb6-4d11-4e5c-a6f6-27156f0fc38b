package ru.naumen.dynamicfield.core.server.criteria;

import java.util.Objects;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;

/**
 * Условие «содержит» для строк в динамических полях.
 * <AUTHOR>
 * @since Feb 01, 2024
 */
public class JsonPathContainsCriterion extends AbstractJsonPathFieldCriterion<String>
{
    private static final String REGEX_SPECIAL_CHARS = "!$()*+.:<=>?[]^{|}-";

    private final boolean ignoreCase;

    public JsonPathContainsCriterion(HColumn column, String fieldPath, String substring, boolean ignoreCase)
    {
        super(column, fieldPath, new String[] { createRegex(substring) });
        this.ignoreCase = ignoreCase;
    }

    @Override
    public boolean equals(Object o)
    {
        return super.equals(o) && o instanceof JsonPathContainsCriterion other && other.ignoreCase == ignoreCase;
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(super.hashCode(), Boolean.toString(ignoreCase));
    }

    private static String createRegex(String substring)
    {
        return ".*" + StringUtilities.escapeWithBackslash(substring, REGEX_SPECIAL_CHARS) + ".*";
    }

    @Override
    protected HCriterion createCopyInstance()
    {
        return new JsonPathContainsCriterion(Objects.requireNonNull(property), fieldPath, parameters[0], ignoreCase);
    }

    @Override
    protected void appendJsonQuery(StringBuilder sb)
    {
        sb.append("@ like_regex ").append(formatParameter(0));
        if (ignoreCase)
        {
            sb.append(" flag \"i\"");
        }
    }
}
