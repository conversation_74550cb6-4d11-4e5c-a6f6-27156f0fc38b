package ru.naumen.dynamicfield.core.server.config.xml;

import java.util.List;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElementWrapper;
import jakarta.xml.bind.annotation.XmlType;

/**
 * Маппинг типов динамических полей.
 * <AUTHOR>
 * @since Jan 17, 2024
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "TypeMapping")
public class TypeMapping
{
    private String sourceType;
    private String destinationType;
    private String destinationPresentation;
    private List<StringMapping> conditions;

    @XmlElementWrapper(name = "when")
    @XmlElement(name = "attribute")
    public List<StringMapping> getConditions()
    {
        return conditions;
    }

    public void setConditions(List<StringMapping> conditions)
    {
        this.conditions = conditions;
    }

    @XmlAttribute(name = "from")
    public String getSourceType()
    {
        return sourceType;
    }

    public void setSourceType(String sourceType)
    {
        this.sourceType = sourceType;
    }

    @XmlAttribute(name = "to")
    public String getDestinationType()
    {
        return destinationType;
    }

    public void setDestinationType(String destinationType)
    {
        this.destinationType = destinationType;
    }

    @Nullable
    @XmlAttribute(name = "presentation")
    public String getDestinationPresentation()
    {
        return destinationPresentation;
    }

    public void setDestinationPresentation(@Nullable String destinationPresentation)
    {
        this.destinationPresentation = destinationPresentation;
    }
}
