package ru.naumen.dynamicfield.core.server.criteria;

import java.util.Objects;

import ru.naumen.core.server.hibernate.HibernateConstants.JsonFunctions;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;

/**
 * Реализация условия фильтрации по диапазону с помощью SQL-функции.
 * <AUTHOR>
 * @since Mar 25, 2024
 */
public class JsonSqlBetweenFilterCriterion extends AbstractJsonSqlFunctionFilterCriterion<Object>
{
    public JsonSqlBetweenFilterCriterion(HColumn property, JsonFieldPath fieldPath, Object begin, Object end)
    {
        super(property, JsonFunctions.JSON_FILTER_BETWEEN, fieldPath, new Object[] { begin, end });
    }

    @Override
    protected HCriterion createCopyInstance()
    {
        return new JsonSqlBetweenFilterCriterion(Objects.requireNonNull(property), fieldPath, parameters[0],
                parameters[1]);
    }
}
