package ru.naumen.dynamicfield.core.server.config;

import java.util.HashMap;
import java.util.Map;

import jakarta.annotation.Nullable;

/**
 * Системный тип поля, применяемый по настроенному условию.
 * <AUTHOR>
 * @since Jan 17, 2024
 */
public class ConditionalType
{
    private String typeName;
    private String presentationName;
    private final Map<String, String> conditions = new HashMap<>();

    public String getTypeName()
    {
        return typeName;
    }

    public void setTypeName(String typeName)
    {
        this.typeName = typeName;
    }

    @Nullable
    public String getPresentationName()
    {
        return presentationName;
    }

    public void setPresentationName(@Nullable String presentationName)
    {
        this.presentationName = presentationName;
    }

    public Map<String, String> getConditions()
    {
        return conditions;
    }
}
