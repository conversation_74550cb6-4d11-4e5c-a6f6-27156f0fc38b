package ru.naumen.dynamicfield.core.server.config.xml;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.XmlValue;

/**
 * Ссылка на атрибут в конфигурации динамических полей.
 * <AUTHOR>
 * @since Jan 31, 2024
 */
@XmlType(name = "AttributeLink")
@XmlAccessorType(XmlAccessType.PROPERTY)
public abstract sealed class AttributeLink permits TemplateAttributeLink, GroupAttributeLink
{
    private String attributePath;

    @XmlValue
    public String getAttributePath()
    {
        return attributePath;
    }

    public void setAttributePath(String attributePath)
    {
        this.attributePath = attributePath;
    }
}
