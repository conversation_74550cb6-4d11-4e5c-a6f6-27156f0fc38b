package ru.naumen.dynamicfield.core.shared;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import ru.naumen.core.shared.attr.presentation.AttributePresentationExtension;
import ru.naumen.core.shared.attr.presentation.AttributePresentationExtensionService;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;

/**
 * Расширение для логики представления значений динамических полей.
 * <AUTHOR>
 * @since Jan 17, 2024
 */
@Singleton
@Component
public class JsonPresentationExtension implements AttributePresentationExtension
{
    @Inject
    public JsonPresentationExtension(AttributePresentationExtensionService extensionService)
    {
        extensionService.registerExtension(this);
    }

    @Override
    public boolean mayHaveMultipleValues(AttributeType attributeType, @Nullable Attribute attribute)
    {
        return attribute != null && DynamicFieldUtils.isDynamicFieldName(attribute.getCode());
    }
}
