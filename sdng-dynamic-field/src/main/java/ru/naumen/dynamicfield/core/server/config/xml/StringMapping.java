package ru.naumen.dynamicfield.core.server.config.xml;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.XmlValue;

/**
 * Маппинг строки на строку.
 * <AUTHOR>
 * @since Jan 17, 2024
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "StringMapping")
public class StringMapping
{
    private String name;
    private String value;

    @XmlAttribute(name = "name")
    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    @XmlValue
    public String getValue()
    {
        return value;
    }

    public void setValue(String value)
    {
        this.value = value;
    }
}
