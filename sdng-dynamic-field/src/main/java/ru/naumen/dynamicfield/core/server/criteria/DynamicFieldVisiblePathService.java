package ru.naumen.dynamicfield.core.server.criteria;

import java.util.List;
import java.util.function.Function;

import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Сервис для работы с путями JSON в запросах к базе данных.
 * <AUTHOR>
 * @since Aor 01, 2024
 */
public interface DynamicFieldVisiblePathService
{
    /**
     * Возвращает список доступных для фильтрации и сортировки путей, описываемых динамическим атрибутом.
     * @param attribute динамический атрибут
     * @return список доступных путей, связанных с данным динамическим атрибутом
     */
    List<JsonFieldPath> getVisiblePaths(Attribute attribute);

    /**
     * Формирует итоговое условие для фильтрации по динамическому атрибуту.
     * @param attribute динамический атрибут
     * @param criterionFactory фабрика условий для отдельных путей
     * @return итоговое условий
     */
    HCriterion buildFinalRestriction(Attribute attribute, Function<JsonFieldPath, HCriterion> criterionFactory);
}
