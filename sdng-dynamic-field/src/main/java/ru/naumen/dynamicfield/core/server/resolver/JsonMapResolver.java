package ru.naumen.dynamicfield.core.server.resolver;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.server.attrdescription.resolvers.ResolverContext;
import ru.naumen.core.server.attrdescription.resolvers.ResolverUtils;
import ru.naumen.dynamicfield.core.server.attribute.DynamicAttributeFactory;
import ru.naumen.dynamicfield.core.server.bo.IDynamicFieldTemplate;
import ru.naumen.dynamicfield.core.server.bo.JsonValueMap;
import ru.naumen.dynamicfield.core.shared.DynamicFieldUtils;
import ru.naumen.metainfo.shared.Constants.FileAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Компонент, преобразующий значения динамических полей к объектам типа «ключ–значение» ({@link IProperties}
 * <AUTHOR>
 * @since Feb 04, 2024
 */
@Component
public class JsonMapResolver
{
    private final ResolverUtils resolverUtils;
    private final DynamicAttributeFactory dynamicAttributeFactory;
    private final JsonValueConverter jsonValueConverter;
    private final JsonFileResolver jsonFileResolver;

    @Inject
    public JsonMapResolver(
            ResolverUtils resolverUtils,
            DynamicAttributeFactory dynamicAttributeFactory,
            JsonValueConverter jsonValueConverter,
            JsonFileResolver jsonFileResolver)
    {
        this.resolverUtils = resolverUtils;
        this.dynamicAttributeFactory = dynamicAttributeFactory;
        this.jsonValueConverter = jsonValueConverter;
        this.jsonFileResolver = jsonFileResolver;
    }

    public JsonValueMap resolveMap(@Nullable Attribute attribute, @Nullable Object rawValue)
    {
        return jsonValueConverter.convert(attribute, rawValue, this::convertValue);
    }

    private Object convertValue(@Nullable Attribute parentAttribute, IDynamicFieldTemplate fieldTemplate,
            @Nullable String typeId, @Nullable Object value)
    {
        Attribute attribute = dynamicAttributeFactory.create(parentAttribute, fieldTemplate, typeId);
        String typeCode = typeId == null ? fieldTemplate.getFieldTypeCode() : DynamicFieldUtils.getTypeCode(typeId);
        if (FileAttributeType.CODE.equals(typeCode))
        {
            return jsonFileResolver.resolve(value);
        }
        else
        {
            return resolverUtils.resolv(new ResolverContext(attribute, value));
        }
    }
}
