/*
 * Some auxillary subroutines for random generating
 */

package ru.naumen.commons.server.utils;

import java.util.Random;

/**
 * <AUTHOR>
 */
public final class RandomTools
{
    private final static Random _rnd = new Random(System.currentTimeMillis());

    public static boolean getBoolRandom()
    {
        return _rnd.nextBoolean();
    }

    public static char getCharacterRandomUniform(char min, char max)
    {
        return (char)getIntRandomUniform(min, max);
    }

    public static double getDoubleRandomUniform(double min, double max)
    {
        if (min > max)
        {
            throw new RuntimeException("min > max"); //$NON-NLS-1$
        }

        double result = _rnd.nextDouble() * (max - min) + min;

        if (result < min || result > max)
        {
            throw new RuntimeException("getIntRandomUniform Internal error"); //$NON-NLS-1$
        }

        return result;
    }

    public static int getIntRandom()
    {
        return _rnd.nextInt();
    }

    public static int getIntRandomUniform(int min, int max)
    {
        if (min > max)
        {
            throw new RuntimeException("min > max"); //$NON-NLS-1$
        }

        int result = _rnd.nextInt(max - min + 1) + min;

        if (result < min || result > max)
        {
            throw new RuntimeException("getIntRandomUniform Internal error"); //$NON-NLS-1$
        }

        return result;
    }

    public static boolean getOccured(double prob)
    {
        if (prob < 0.0)
        {
            throw new RuntimeException("prob < 0"); //$NON-NLS-1$
        }
        if (prob > 1.0)
        {
            throw new RuntimeException("prob > 1"); //$NON-NLS-1$
        }

        return _rnd.nextDouble() < prob;
    }

    public static String getStringRandomDay()
    {
        return StringUtilities.appendToLen(getIntRandomUniform(1, 28), 2);
    }

    public static String getStringRandomEn(int len)
    {
        return getStringRandomUniform('a', 'z', len);
    }

    public static String getStringRandomMonth()
    {
        return StringUtilities.appendToLen(getIntRandomUniform(1, 12), 2);
    }

    public static String getStringRandomNumber(int len)
    {
        return getStringRandomUniform('0', '9', len);
    }

    public static String getStringRandomRu(int len)
    {
        //А я
        return getStringRandomUniform('\u0410', '\u044f', len);
    }

    public static String getStringRandomUniform(char min, char max, int len)
    {
        if (len < 0)
        {
            throw new RuntimeException("len < 0"); //$NON-NLS-1$
        }

        StringBuilder builder = new StringBuilder(len);
        for (int i = 0; i < len; ++i)
        {
            builder.append(getCharacterRandomUniform(min, max));
        }
        return builder.toString();
    }

    public static String getStringRandomYear(int fromYear, int toYear)
    {
        return "" + getIntRandomUniform(fromYear, toYear); //$NON-NLS-1$
    }

    private RandomTools()
    {

    }
}