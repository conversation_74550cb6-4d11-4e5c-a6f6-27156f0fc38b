package ru.naumen.core.server.script.api.dynfield;

import java.util.Collection;

import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.ISProperties;
import ru.naumen.core.server.script.ScriptApi;

/**
 * API динамических полей.
 * <AUTHOR>
 * @since Feb 05, 2024
 */
public interface IDynamicFieldApi extends ScriptApi
{
    /**
     * Функция, определяющая необходимость замены значений.
     */
    @FunctionalInterface
    interface ValueReplacePredicate
    {
        /**
         * Определяет, нужно ли существующее значение заменять новым.
         * @param existingValue существующее значение
         * @param newValue новое значение
         * @return <code>true</code>, если существующее значение нужно заменить новым, иначе <code>false</code>
         */
        boolean needReplace(ISProperties existingValue, ISProperties newValue);
    }

    /**
     * Функция, определяющая необходимость удаления значений.
     */
    @FunctionalInterface
    interface ValueRemovePredicate
    {
        /**
         * Определяет, нужно ли удалять значение.
         * @param value проверяемое значение
         * @return <code>true</code>, если значение нужно удалить, иначе <code>false</code>
         */
        boolean needRemove(ISProperties value);
    }

    /**
     * Возвращает идентификатор системного типа для указанного шаблона динамического поля.
     * @param template шаблон динамического поля
     * @return идентификатор системного типа шаблона или <code>null</code>, если переданные объект не является шаблоном
     */
    @Nullable
    String getTypeId(Object template);

    /**
     * Выполняет слияние новых значений динамических полей с уже существующими.
     * @param existingValue существующее значение атрибута
     * @param newValues новые значения динамических полей
     * @return новое значение атрибута после слияния
     */
    ISProperties mergeDynamicFieldValues(Object existingValue, Object newValues);

    /**
     * Выполняет слияние новых значений динамических полей с уже существующими.
     * @param existingValue существующее значение атрибута
     * @param newValues новые значения динамических полей
     * @param valueReplaceFunction функция слияния значений: сравнивает два значения и определяет, нужно ли заменить
     * старое значение новым, если они относятся к одному и тому же шаблону
     * @return новое значение атрибута после слияния
     */
    ISProperties mergeDynamicFieldValues(Object existingValue, Object newValues,
            @Nullable ValueReplacePredicate valueReplaceFunction);

    /**
     * Удаляет значения динамических полей по указанным идентификаторам шаблонов.
     * @param value существующее значение атрибута
     * @param templateUuids идентификаторы (UUID) шаблонов
     * @return новое значение атрибута после удаления
     */
    ISProperties removeDynamicFieldValues(Object value, Collection<String> templateUuids);

    /**
     * Удаляет значения динамических полей по указанным идентификаторам шаблонов.
     * @param value существующее значение атрибута
     * @param templateUuids идентификаторы (UUID) шаблонов
     * @param removePredicate функция, определяющая необходимость удаления конкретного значения
     * @return новое значение атрибута после удаления
     */
    ISProperties removeDynamicFieldValues(Object value, Collection<String> templateUuids,
            @Nullable ValueRemovePredicate removePredicate);

    /**
     * Перезагружает конфигурацию динамических полей.
     * Ход перезагрузки можно отследить в логе приложения.
     * @return <code>true</code>, если изменения были применены и ошибок не возникло, иначе <code>false</code>
     */
    boolean reloadConfiguration();
}
