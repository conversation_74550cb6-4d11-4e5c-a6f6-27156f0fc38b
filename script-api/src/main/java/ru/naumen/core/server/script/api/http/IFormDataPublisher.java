package ru.naumen.core.server.script.api.http;

import java.io.InputStream;
import java.net.http.HttpRequest.BodyPublisher;
import java.util.function.Supplier;

/**
 * Интерфейс для создания и добавления частей в составное тело запроса
 * в формате {@code multipart/form-data}.
 *
 * <AUTHOR>
 * @since 28.08.2024
 */
public interface IFormDataPublisher extends BodyPublisher
{
    /**
     * Добавление данных в текстовом формате.
     *
     * @param name название поля
     * @param value значение поля
     * @return this
     */
    IFormDataPublisher addText(String name, String value);

    /**
     * Добавление данных из {@link InputStream}.
     *
     * @param name название поля
     * @param filename название файла
     * @param supplier для формирования {@link InputStream}
     * @return this
     */
    IFormDataPublisher addStream(String name, String filename,
            Supplier<InputStream> supplier);

    /**
     * Добавление данных из {@link InputStream}, с указанием типа содержимого.
     *
     * @param name название поля
     * @param filename название файла
     * @param contentType тип содержимого
     * @param supplier для формирования {@link InputStream}
     * @return this
     */
    IFormDataPublisher addStream(String name, String filename, String contentType,
            Supplier<InputStream> supplier);

    /**
     * Возвращает строку, в которой указан формат - multipart/form-data и сгенерированный boundary.
     *
     * @return строка формата: "multipart/form-data; boundary=сгенерированный boundary"
     */
    String contentType();
}