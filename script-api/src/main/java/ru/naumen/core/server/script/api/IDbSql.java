package ru.naumen.core.server.script.api;

import ru.naumen.core.server.script.api.IDbApi.IQuery;

/**
 * Содержит методы API для выполнения SQL-запросов в БД
 *
 * <AUTHOR>
 * @since 15.11.2024
 */
@SuppressWarnings("InterfaceMayBeAnnotatedFunctional")
public interface IDbSql
{
    /**
     * Интерфейс для выполнения SQL-запросов через Hibernate
     */
    interface ISqlQuery extends IBaseQuery<ISqlQuery>
    {
        /**
         * Указывает название сущностей, которые будут затронуты запросом.
         * При использовании {@link #executeUpdate()} использование обязательно.
         *
         * @param entityName название сущности
         *
         * @since 4.19.5
         */
        ISqlQuery addSynchronizedEntityName(String entityName);
    }

    /**
     * Позволяет выполнить SQL запрос.
     *
     * @param query текст запроса на языке SQL
     * @return обёртку запроса {@link IQuery}
     * @see
     * <a href="https://docs.jboss.org/hibernate/orm/6.6/javadocs/org/hibernate/query/QueryProducer.html#createNativeQuery(java.lang.String,java.lang.Class)">подробнее про тип результата.</a>
     *
     * @since 4.19.5
     */
    ISqlQuery query(String query);
}
