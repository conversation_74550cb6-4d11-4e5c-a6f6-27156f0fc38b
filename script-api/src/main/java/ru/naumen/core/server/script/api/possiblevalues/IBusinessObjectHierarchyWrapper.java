package ru.naumen.core.server.script.api.possiblevalues;

import ru.naumen.core.shared.ISHasMetainfo;
import ru.naumen.core.shared.ISTitled;
import ru.naumen.core.shared.IUUIDIdentifiable;

/**
 * Узел иерархии возможных значений, состоящей из бизнес-объектов.
 * Содержит UUID, тип объекта, название и узлы дочерних элементов.
 *
 * <AUTHOR>
 * @since 04.03.2024
 */
public interface IBusinessObjectHierarchyWrapper extends ISHasMetainfo, IUUIDIdentifiable, ISTitled, IHierarchyWrapper
{
}
