package ru.naumen.core.server.script.api.scheduler;

import java.util.Date;

import ru.naumen.core.server.script.api.IDocumentedApi;

/**
 * Документированный класс по работе с информацией о задаче планировщика
 * {@link ru.naumen.metainfo.shared.scheduler.SchedulerTask} для использования в скриптах
 * @see IDocumentedApi
 * <AUTHOR>
 * @since 10.04.2025
 */
public interface IDocumentedSchedulerTaskWrapper extends IDocumentedApi
{
    /**
     * @return UUID задачи планировщика
     */
    String getCode();

    /**
     * @return описание задачи планировщика
     */
    String getDescription();

    /**
     * @return дата последнего выполнения задачи планировщика
     */
    Date getLastExecutionDate();

    /**
     * @return дата следующего запуска задачи планировщика
     */
    Date getPlanDate();

    /**
     * @return название задачи планировщика
     */
    String getTitle();

    /**
     * @return список правил выполнения задачи планировщика
     */
    Iterable<ITriggerWrapper> getTrigger();

    /**
     * @return правило выполнения задачи планировщика по коду правила
     */
    ITriggerWrapper getTrigger(String triggerCode);
}
