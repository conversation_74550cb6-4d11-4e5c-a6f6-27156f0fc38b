package ru.naumen.core.server.script.api.maintenance;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.script.ScriptApi;

/**
 * API для работы с режимом обслуживания
 *
 * <AUTHOR>
 * @since 20.11.2024
 */
public interface IMaintenanceApi extends ScriptApi
{
    /**
     * Возвращает информацию о режиме обслуживания.
     * В случае, если режим обслуживания не запланирован и не активен, возвращает null.
     */
    @Nullable
    IMaintenanceInfo getMaintenanceInfo();
}
