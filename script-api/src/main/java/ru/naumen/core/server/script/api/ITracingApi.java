package ru.naumen.core.server.script.api;

import ru.naumen.core.server.script.ScriptApi;

/**
 * API трассировки для отслеживания последовательности выполнения действий
 *
 * <AUTHOR>
 */
public interface ITracingApi extends ScriptApi
{
    /**
     * Возвращает строку текущего traceId,
     * который представляет из себя идентификатор определенной последовательности событий
     */
    String getTraceId();

    /**
     * Возвращает строку текущего spanId, который представляет из себя идентификатор процесса
     */
    String getSpanId();

    /**
     * Возвращает строку аналогичную выводимой в логе
     */
    String getTraceString();
}
