package ru.naumen.core.server.script.api.possiblevalues;

import ru.naumen.core.shared.ISTitled;
import ru.naumen.metainfo.shared.IClassFqn;

/**
 * Узел иерархии возможных значений, состоящей из метаклассов.
 * Содержит FQN, код класса/типа, название и узлы дочерних элементов.
 *
 * <AUTHOR>
 * @since 04.12.2024
 */
public interface IMetaClassHierarchyWrapper extends ISTitled, IHierarchyWrapper
{
    /**
     * Возвращает FQN класса/типа
     */
    IClassFqn getFqn();

    /**
     * Возвращает FQN класса/типа в виде строки
     */
    String getCode();
}
