package ru.naumen.metainfo.shared;

import java.io.Serializable;

import jakarta.annotation.Nullable;
import ru.naumen.core.shared.HasClone;

public interface IClassFqn extends IClassFqnWrapper, CoreClassFqn, Cloneable, Serializable, Fqn, HasClone
{
    /**
     * @return {@link IClassFqn} соответсвующий Классу в иерархии метаклассов относящегося к текущему {@link IClassFqn}
     */
    @Override
    IClassFqn fqnOfClass();

    /**
     * @return @return <pre>true</pre> - когда {@link IClassFqn}  Типу, false - соответствует классу
     */
    default boolean isCase()
    {
        return !isClass();
    }

    /**
     * Признак совпадения типа текущего идентификатора с типом переданного идентификатора
     * @param other Идентификатор для сравнения
     * @return <code>true</code> Типы совпадают, <code>false</code> Типы не совпадают
     */
    boolean isCaseOf(@Nullable CoreClassFqn other);

    /**
     * Признак совпадения класса текущего идентификатора с классом переданного идентификатора
     * @param other Идентификатор для сравнения
     * @return <code>true</code> Классы совпадают, <code>false</code> Классы не совпадают
     */
    boolean isClassOf(@Nullable CoreClassFqn other);

    /**
     * @see #isSameClass(CoreClassFqn)
     * @deprecated имя данного метода содержит ошибку. Пожалуйста, пользуйтесь {@link #isSameClass}
     */
    @Deprecated
    boolean isSomeClass(IClassFqn other);

    /**
     * Проверяет, что текущий {@link IClassFqn объект} и {@link CoreClassFqn other} относятся к одному классу
     * @param other объект для проверки принадлежности к одному классу с текущим
     * @return true если этот {@link IClassFqn объект} и {@link CoreClassFqn other} относятся к одному классу
     */
    @Override
    boolean isSameClass(@Nullable CoreClassFqn other);

}
