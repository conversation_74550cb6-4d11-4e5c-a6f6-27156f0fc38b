Comment=Коментар
aboveAndUnderList=Над списком та під списком
aboveList=Над списком
add=Додати
addAttrColumn=Щоб додати кнопку виберіть назву та натисніть "+"
addColumn=Додати
addComment=Додати коментар
addCommentStateSettings=Коментарі
addFile=Додати файл
addFolder=Додати папку
addFolderToFavorites=Додавання папки у вибране
addItem=Додати елемент
addLink=Додати
addNewTab=Додати нову вкладку
addObject=Додати об''єкт
addObject[employee]=Додати співробітника
addObject[ou]=Додати відділ
addObject[team]=Додати команду
addRel=перенести до обраних
addRelation=Додати зв''язок
addSc=Додати запит
addSuperUser=Додавання суперкористувача
addTab=Додати вкладку
addThis=Додати {0}
addToFavorites=Додати в обране
addingLinkFormCaption=Назва форми додавання зв''язку
addingPageToFavorites=Додавання сторінки в обране
adminInterface=Інтерфейс адміністратора
advImportAddConfiguration=Додавання конфігурації
advImportConfiguration=Конфигурація в XML-вигляді
advImportConfigurationTitle=Опис синхронізації
advImportEditConfiguration=Редагувати конфігурацію
advSearchHelpHint=Інформація про мову просунутого пошуку
advancedSearchHelpHtml=Інформація про мову пошуку<br><br>? - замінює 1 символ тексту (неможна використовувати на початку пошукового запиту).<br>* - замінює від нуля і більше символів (неможна використовувати на початку пошукового запиту).<br>/.+example.+/ - конструкція такого виду шукає збіг з пошуковим запитом _example_ тільки в середині слова.<br><br>Логічні оператори:<br><br>OR - оператор АБО, результатом пошуку буде об''єкт, в якому є збіг хоча б з одним із заданих слів.<br>AND - оператор І, результатом пошуку буде об''єкт, в якому є збіг з кожним заданим словом.<br>NOT - оператор виключення, результатом пошуку буде об''єкт, в якому нема збігу із заданим словом (неможа використовувати на початку пошукового запиту.<br>() - задають черговість виконання логічних операторів.<br><br>Спеціальні символи:<br><br>\\ + - && || ! ( ) '{' '}' [ ] ^ " ~ * ? \\\\ / можно екранувати, поставивши перед кожним з них \\
advancedSearchHelpText=Інформація про мову просунутого пошуку\n\n? - замінює 1 символ текста (неможна використовувати на початку пошукового запиту).\n* - замінює від нуля та більше символів (неможна  використовувати на початку пошукового запиту).\n/.+example.+/ - конструкція такого виду шукає співпадіння з пошуковим  запитом _example_ тільки в середині слова.\n\nЛогічні оператори:\n\nOR - оператор АБО, результатом пошуку буде об''єкт, в якому є співпадіння хоча б з одним із заданих слів.\nAND - оператор І, результатом поошуку буде об''єкт, в якому є співпадіння з кожним заданим словом.\nNOT - оператор виключення, результатом пошуку буде об''єкт, в якому нема співпадіння з заданим словом (неможливо використовувати на початку пошукового запиту).\n() - задають черговість виконання поповнення логічних операторів.\n\nСпеціальні символи:\n\n+ - && || ! ( ) '{' '}' [ ] ^ " ~ * ? \\\\ / можна екранувати, поставивши перед кожним з них \\
advlistFieldSetting=Налаштування полів
agreement=Угода
agreementAndService=Угода/Послуга
all=Усі
allItemsCount=всього елементів: {0}
allItemsHasMore=всього елементів більше {0}. Уточніть пошук
allResults=Всі результати
allSettings=Всі налаштування
allowEditRelObj=Дозволити редагування пов''язаного об''єкту
and=та
any=Будь-який
anyState=будь-який статус
anyValue=Будь-яке значення
appId=Ідентифікатор додатку
apply=Примінити
applyCmdEnter=Примінити (Cmd + Enter)
applyCtrlEnter=Примінити (Ctrl + Enter)
applyEnter=Примінити  (Enter)
arch=арх.
association=Прив''язка
attachedFiles=Прикріплено файли: {0}
attention=Увага!
attribute=Атрибут
attributeGroup=Група атрибутів
attributeLink=Атрибут зв''язку
attributeToLowerCase=атрибут
attributes=Атрибути
authentication=Аутентифікація
author=Автор
b=Б
back=Назад
black=Чорний
blue=Синій
brackets=[{0}]
breadCrumbElement=Елемент "хлібні крихти"
breakLink=Розірвати зв''язок
buildNumber=Збірка
cancel=Відміна
cancelChanges=Скасувати зміни
cancelEsc=Відміна (Esc)
cannotChangeResponsible=Об''єкт "{0}" не може бути змінений з наступних причин: у Вас немає прав на зміну відповідального за об''єкт класу/типу "{1}".
cannotChangeState=Об''єкт "{0}" не може бути змінений з таких причин: у Вас немає прав на зміну статусу об''єкта класу/типу "{1}".
catalog=Довідник
catalogCode=Код
catalogItem=Елемент довідника
catalogToLowerCase=довідник
catalogs=Довідники
change=Змінити
changeAdvimportConnectionPassword=Змінити пароль підключення
changeAssociation=Змінити прив''язку
changePassword=Змінити пароль
changeResponsibleButton=Показувати кнопку зміни відповідального
changeState=Змінити статус
changeThis=Змінити {0}
changeType=Змінити тип
checkPermissionEmployee=Враховувати права співробітника
chooseFile=Вибрати файл
chooseFolderForFavorites=Вибір папки вибраного
classOfService=Клас обслуговування
classTitle=Номер
classTitleMultiple=Класи
classTitleMultiple[agreement]=Угоди
classTitleMultiple[employee]=Співробітники
classTitleMultiple[ou]=Відділи
classTitleMultiple[serviceCall]=Запити
classTitleMultiple[slmService]=Послуги
classTitleMultiple[team]=Команди
classTitle[agreement]=Угода
classTitle[employee]=Співробітник
classTitle[ou]=Відділ
classTitle[serviceCall]=Запит
classTitle[slmService]=Послуга
classTitle[team]=Команда
classTypeUsage=Клас/Типи
classTypes=Типи об''єктів
classTypesOnAttrShowUsageForm=Клас/Типи
classes=Класи
clazz=Клас
clazzOfToLowerCase=класу
clazzToLowerCase=клас
clear=Очистити
client=Контрагент
clientId=Ідентифікатор каталога
clientSecret=Секрет клієнта
close=Закрити
closeEsc=Закрити (Esc)
code=Код
collapse=Згорнути
color=Колір
columnsSetting=Налаштувати поля
commands=Набір команд
comment=Коментар
commentListWasChanged=Список було змінено
commentsLimitReached={0}: Досягнуто ліміт на кількість коментарів до об''єкту.
commentsLimitReachedMassProblem=Коментар не може бути доданий для наступних об''єктів класу "{0}" : {1}. Досягнуто ліміт на кількість коментарів об''єкту.
commonRestrictionFuture=в майбутньому
commonRestrictionPast=в минулому
complexForm=Складна форма
complexRelationInAttributes=Розширене редагування зв''язків для атрибутів
complexRelationInParameterNoun=Розширене редагування зв''язків для параметра "{0}"
complexRelationInParameters=Розширене редагування зв''язків для параметрів
complexRelationNoun=Розширене редагування зв''язків для атрибута
computable=Обчислювальний
condition=Умова
conditionNotNull=заповнено
conditionNull=не заповнений
conditionToday=сьогодні
confirmAction=Підтвердження дії
confirmActionQuestion=Модулі з наступними кодами вже існують: {0}. Оновити існуючі модулі?
confirmApprove=Підтвердження ствердження
confirmDelete=Підтвердження видалення
confirmDeleteAggregateAttrPart=Разом з ним будуть видалені атрибути: {0}
confirmDeleteLink=Підтвердження розриву зв''язку
confirmDeleteLinkQuestion=Ви дійсно хочете розірвати зв''язок між об''єктами ''{0}'' и ''{1}''?
confirmDeleteQuestion=Ви дійсно хочете видалити {0} "{1}"?
confirmDeleteQuestion2=Ви дійсно хочете видалити {0}?
confirmDeleteValueMapRow=Ви дійсно хочете видалити рядок таблиці відповідностей?
confirmEditSettings=Підтвердження дозволу редагування
confirmEditSettingsQuestion=Ви справді хочете дозволити редагування налаштувань {0}?
confirmRemove=Підтвердження архівування
confirmRemoveQuestion=Ви дійсно хочете помістити в архів {0} ''{1}''?
confirmResetSettings=Підтвердження скидання налаштувань
confirmResetSettingsQuestion=Ви дійсно хочете відновити успадкування налаштувань {0}?
confirmRestore=Підтвердження відновлення з архіву
confirmRestoreQuestion=Ви дійсно хочете відновити з архіву {0} ''{1}''?
confirmRun=Підтвердження виконання дії
confirmUseSystemSettingsQuestion=Ви дійсно хочете відновити системні налаштування {0}?
connectionParameters=Параметри підключення
contacts=Контактні особи
contain=містять...
content=Зміст
contentNoun=контент
contentTitle=Контент
contentUsagePlace=Контент "{0}" на вкладці "{1}"
copiedFrom=Скопійований з...
copiedTo=Скопійований до...
copiedToSlaves=скопійовано у ведені об''єкти
copy=Копіювати
copyLink=Копіювання посилання на список об''єктів допустимо тільки з картки об''єкта
copyLinkToList=Скопіювати посилання на список
copyLinkToListNotAvailable=Копіювання посилання
copyPermissionsFromExistingPermissionsMarker=Скопіювати права із існуючого маркера прав
copyThis=Копіювати {0}
copyToMasterObject=Копіювати у ведучий об''єкт
copyToSlaveObjects=Копіювати до відомих об''єктів
currentColor=Поточний колір
date=Дата
dayShort=дн.
days=днів
defaultAddingLinkFormCaption=Додавання зв''язку з об''єктом класу {0}
defaultValue=За замовчуванням
delGenericMessageEnd=Разом з об''єктом будуть видалені всі вкладені в нього об''єкти.
delOuMessageEnd=Разом з відділом будуть видалені вкладені в нього відділи й співробітники.
delete=Видалити
deleteClass=Разом з класом будуть видалені усі вкладені у нього типи
deleteClassClearParent=При видаленні батьківського класу в них буде очищено атрибут "Батьківський об''єкт" і вони перестануть бути вкладеними.
deleteClassMessage=Разом з класом будуть видалені вкладені в нього типи
deleteClassWithCases=Разом з класом будуть видалені усі вкладені у нього типи: {0}
deleteClassWithInnerClasses=У даного класу є вкладені класи: {0}
deleteFolderCatalogItemMessage=Вкладені папки будуть видалені разом із папкою
deleteFolderMessage=Разом з папкою будуть видалені вкладені в неї папки і елементи довідника
deleteLink=Видалити зв''язок
deleteRel=перенести з обраних
deleteRelation=Видалити зв''язок
deleteRow=Видалити рядок?
deleteTabMessage=Разом з вкладкою будуть видалені усі об''єкти на ній.
deleteThis=Видалити {0}
deleteToLowerCase=видалити
deleteTypeMessage=Разом з типом будуть видалені всі вкладені в нього .
deleteTypeMessageWithCases=Разом з типом будуть видалені усі вкладені у нього типи: {0}
dependentProblems=Пов''язані запити
description=Опис
dialogHideDetails=Приховати
dialogNote={0} - обов''язкові для заповнення поля
dialogShowDetails=Детальніше
digitsRestrictionPlaceholder=Зазначте ціле число від 1 до 10
disableAdvancedSearch=Відключити мову просунутого пошуку
disabled=Вимкнено
disconnect=Відключити
displayListOnSeparatePage=Відобразити список на окремій сторінці
doYouReallyWantToLeaveThePage=Ви дійсно хочете покинути сторінку? Усі внесені зміни будуть втрачені.
documentation=Documentation
dot=Крапка
download=скачати
downloadFile=Завантажити файл
dragFilesHere=Перетягніть файли сюди
draggable=Перетягти
edit=Редагувати
editAdvlistDefaultPrs=Налаштувати зовнішній вигляд за замовчуванням
editAttributes=Редагування
editCommentFormCaption=Редагування коментаря
editContentDialogCaption=Зміна контента
editFavorites=Редагування обраного
editFavoritesItem=Редагування {0}
editObjectCaption=Редагування заголовку об''єкта
editProperties=Редагувати властивості
editResponsible=Змінити відповідального
editTabs=Редагувати вкладки
editTabsCaption=Редагування вкладок
editThis=Редагувати {0}
editing=Редагувати
elementView=Вигляд елемента
elseTab=Ще
email=email:{0}
emailAddress=Адреса електронної пошти
employee=Співробітник
empty=не зазначено
emptyFile=Файл не було завантажено. Неможливо завантажити порожній файл.
emptyFileWithName=Файл не було завантажено. Неможливо додати порожній файл "{0}".
emptySearchResult=За таким запитом нічого не знайдено. Переконатись, що всі слова написані без помилок.
enableAdvancedSearch=Використовувати мову просунутого пошуку
enabled=Ввімкнено
enterFormMessage=Виклик форми "{0}" для об''єкту класу {1}
errorLargeFile=Файл не було завантажено. Розмір файлу перевищує обмеження, задане адміністратором системи ({0} Мб).
errorLargeFileBasic=Файл не було завантажено. Розмір файлу перевищує обмеження, задане адміністратором системи.
etc=і т.д.
eventAction=Дія по події
exceed=минув
executionParameters=Параметри виконання
exitFromLayoutMode=Вийти з режиму розмітки
expand=Розгорнути
export=Вивантажити
exportAdvlist=Експорт списку
exportAttentionMessage=Кількість рядків перевищує максимальне значення. Були проекспортовані перші 1000 рядків.
exportConfirmMessageConfines=Кількість експортованих рядків перевищує максимально допустиме значення. Будуть експортовані тільки перші {0} рядків з {1}.
exportConfirmMessageMany=Виконується спроба експорту більше {0} рядків.
exportEmail=Вкажіть адресу електронної пошти. На цю адресу буде відправлено посилання на скачування файлу з експортованими даними:
exportHierarchy=Експорт ієрархічного списку
exportHierarchyConfirmMessage=Кількість записів, що експортуються: {0}
exportHierarchyItemsCountWarning=Кількість рядків, що експортуються, перевищує максимально допустиме значення. Експортувати будуть лише перші {0} рядків.
extendedSearch=Розширений пошук
extendedSearchParams=Параметри пошуку
fastFilter=Швидка фільтрація по атрибуту
fastLinkNoun=Налаштування згадування об''єктів
fastLinkSetting=Налаштування згадування об''єктів
favorites=Вибране
favoritesDuplicate=Дана сторінка вже є у вашому Обраному: \n{0}
field=Поле
fieldSettings=Налаштування полів сортування
file=Файл
fileUploadError=Файл не може бути завантажений: {0}
fileCannotBeLoaded=Файл не було завантажено. Можливі причина: розмір файлу більше допустимого ({0} МБ), файлова система недоступна для запису.
fileFormat=Формат файлу
filter=Фільтр
filtration=Фільтрація
folder=папку
folderFromFavorites=папку ''{0}'' і весь її вміст із вашого Вибраного
folderIcons=Папка
foldersCatalog=Каталоги
font=Шрифт
fontSize=Розмір
forLast=за останні
forSearchByPressEnter=для пошуку за "{0}" натисніть Enter
forSort=сортування
forView=для перегляду
forbidSelectTheme=Заборонити користувачам вибирати тему
formAttentionText=Перед заповненням виконайте декілька дій. Перелік дій для виконання.
formMinimize=Згорнути (Ctrl + M)
formRestore=Розгорнути (Ctrl + Е)
formTitle=Форма {0}
found=знайдено {0} з {1}
foundBy=знайдено по "{0}":
foundItems=знайдено елеметнів: {0}
foundItemsHasMore=знайдено більше {0} елементів. Уточніть пошук
foundShort=знайдено {0}
freezeProfile=Закріпити профіль
from=з
gb=ГБ
goToTheSetup=Перейти до налаштування
greater=більше
green=Зелений
hasMoreElements=У списку показані перші 20 пов''язаних об''єктів, всього таких об''єктів більше
hasNoResponsible=Об''єкт "{0}" не може бути змінений з таких причин: для об''єктів класу "{1}" не можна призначити відповідального.
hasNoWorkflow=Об''єкт ''{0}'' не може бути змінений з наступних причин: у об''єктів класу ''{1}'' немає життєвого циклу.
haveNoChangeStatePermission=У вас немає дозволів на перехід із статусу "{0}" в статус "{1}" в класі/типі "{2}".
haveNoPermissionTo=У Вас нема прав на {0}
haveNoPermissionToPerformOperation=У Вас немає прав на виконання цієї операції
helpSearchTip=Для пошуку та вибору значення натисніть Enter або клацніть у полі
hide=приховати
hideAttrs=Згорнути
hideByDefault=Приховати за замовчуванням
hideMenu=Приховати меню
hint=Підказка
history=Історія
hour=годин
hourShort=г.
icon=Зображення
iconOriginal=Вихідний файл іконки
icons=Піктограми
id=ID
inNext=в найближчі
incorrectFileExtensions=Неприпустимий формат файлу. Виберіть файл одного з наступних форматів: {0}.
incorrectFilter=Некоректний фільтр
inheriteInternalScrolling=Наслідувати від загального налаштування
invalidSearchLengthAnd=і
invalidSearchLengthFull=Довжина пошукового рядка повинна бути {0}{1}{2}. Змініть рядок пошуку.
invalidSearchLengthNotLess=не менше {0}
invalidSearchLengthNotMore=не більше {0}
isNot=не
itemOfCatalog=елемент довідника
itemsCount=елементи: {0}
kb=КБ
layoutMode=Режим розмітки
less=менше
level=Рівень
license=Ліцензія
licenseNotExists={0} (відсутній)
link=Посилання
linkLifetimeExpired=Час життя посилання закінчився
linkToListSuccessfullyCopied=Посилання на список успішно скопійовано
list=Список
listTemplateNoun=Шаблон списку
locationForUploadingFilesIsIncorrect=Місце збереження файлу налаштовано не правильно, зверніться до адміністратора системи.
log=Лог
login=Логін
loginAsUser=Увійти під співробітником
loginAsUserCaption=Вхід під співробітником
manyObjectAttention=Знайдено понад {0} об''єктів. У результатах пошуку відображаються перші {0}. Уточніть параметри пошуку.
massCall=перенести в підлеглі
massEdit=Масове редагування
master=Ведучий
mb=МБ
mcDefParamsUncomformSCParamsWarnMessage=Параметри запиту за замовчуванням налаштовані некоректно: {0}.\nПоточне налаштування не діє при створенні запитів, можливо, налаштування слід змінити.
mcDefParamsUncomformSCParamsWarnMessageAgreement=в полі "Угода/Послуга" має бути вибрано "Угода"
mcDefParamsUncomformSCParamsWarnMessageOrder=поле "{0}" має бути заповненим
mcDefParamsUncomformSCParamsWarnMessageService=в полі "Угода/Послуга" має бути вибрано "Послуга"
menu=Меню
menuAbbreviation=Скорочення
menuIcon=Іконка
menuPresentation=Маркер
metaCase=Тип об''єкта
metaCaseOfLowerCase=типу
metaCaseShort=Тип
metaCasesShort=Типи
metaChildren=Вкладені класи
metaClass=МетаКлас
metric=Метрика
minute=хвилин
minuteShort=хв.
mobileAddFormUsagePlace=Форма додавання в мобільному додатку
mobileEditFormUsagePlace=Форма редагування в мобільному додатку
mobileListCardUsagePlace=Список об''єктів у мобільному додатку
mobileObjectCardUsagePlace=Карточка об''єкта в мобільному додатку
model=Модель
more=Детальніше
moreMenuItem=Ще
move=Перемістити
moveLeft=Перемістити вліво
moveRight=Перемістити праворуч
nameRule=Правило найметування
nameRuleFull=Правило формування атрибуту<br/>"{0}" (title)
newColor=Новий колір
newFolderTitle=Назва нової папки
newObject=[новий об''єкт]
newTab=Нова вкладка
newType=Новий тип
no=ні
noCaption=без заголовку
noElements=[немає елементів]
noExceed=не минув
noFilesSelected=Немає вибраних файлів
noFirstLetterUpperCase=Ні
noStateWithCode=Об''єкт ''{0}'' не може бути змінений з таких причин: статус з кодом ''{1}'' не визначено.
noone=Жоден
notLicensedUser=Неліцензований користувач
notUseMassOperation=Не використовувати панель масових операцій
numberRule=Правило формування номера
numberRuleFull=Правило формування атрибуту<br/>"{0}" (number)
oauth=Аутентифікація OAuth2
objecClass=Клас об''єктів
object=об''єкт
objectAlreadyIsInNewState=Перехід до статусу "{0}" не може бути виконано. Об''єкт {1} вже знаходиться у новому статусі.
objectCaption=Заголовок об''єкту
objectClasses=Класи об''єктів
objectDeleted=об''єкти видалений
objectIsRemoved=Об''єкт знаходиться в архіві
objectListAdvlist=Важкий список
objectListDefault=Простий список
objectTitledWillBeAvailableAfterSave=Дію "{0}" для об''єкта "{1}" не виконано: дії з об''єктом стануть доступними після збереження основної форми.
objectType=Тип об''єкта
objectWillBeAvailableAfterSave=Дії з об''єктом стануть доступними після збереження основної форми.
objects=Об''єкти
objectsTransitionError=Перехід до статусу ''{0}'' не може бути виконаний для наступних об''єктів класу ''{1}'':
objectsWillBeAvailableAfterSave=Дію "{0}" для об''єктів "{1}" не виконано: дії з об''єктом стануть доступними після збереження основної форми.
ofChildrenCases=вкладених типів
ofEditForm=Форми редагування
ofFlexes=користувацьких атрибутів
ofGroup=групи атрибутів ''{0}''
ofGroups=груп атрибутів
ofNewForm=форми додавання
ofObjectCard=карточки об''єкта
ofSystemAttr=системних атрибутів
off=Вимкнено
ok=ОК
on=Ввімкнено
onTabNoun=на вкладці
openMassServiceCallForm=Работа з масовістю
operationForbidden=У вас немає дозволів на цю операцію
operationUnauthorized=Помилка авторизації, можливо вичерпалася сесія. Необхідно заново війти в систему.
or=або
others=Інші
pageFromFavorites=сторінку ''{0}'' з обраного
pagerFrom=з
pagingLocationSettings=Розташування посторінкової навігації
parent=Батьківський об''єкт
password=Пароль
passwordChange=Зміна пароля
passwordConfirm=Підтвердження пароля
permissionExpirationDate=Дата закінчення прав
permissionMarkerForEditAttr=Маркер прав "{0}" на редагування атрибутів
permissionMarkerForViewAttr=Маркер прав "{0}" на перегляд атрибутів
permissionsMarkerForCopy=Маркер прав для копіювання
permitSelectTheme=Дозволити користувачам вибирати тему
phones=т.{0}
pin=Створити плитку для панелі швидкого доступу
placeUnaccessible=Даний перехід неможливо здійснити для суперкористувача
port=Порт
presentation=Подання
print=Друк
profileVers=[план.]
properties=Властивості
protocol=Протокол
putToFolder=Вкласти до папки
question=питання
quotes="{0}"
red=Червоний
refresh=Оновити
refreshList=Оновлювати список
reindex=Переіндексувати
remove=Помістити до архіву
removeCaption=Архів. {0}
removeClassMessage=Разом з класом будуть архівовані в нього типи.
removeFolderCatalogMessage=Разом з папкою будуть переміщені до архівувкладені в неї папки
removeGenericMessageEnd=Усі вкладені об''єкти будуть архівовані разом із об''єктом.
removeOuMessageEnd=Вкладені відділи та співробітники будуть архівуватися разом із відділом.
removeThis=Помістити {0} до архіву
removeTypeMessage=Вкладені типи будуть архівовані разом із типом.
removed=Архівний
removedShort=(арх.) {0}
rename=Перейменувати
reset=Скинути
resetFlexSettings=Скинути налаштування атрибутів користувача
resetSearchQuery=Скинути пошуковий запит
resetSettings=Скинути налаштування
resetSystemSettings=Скинути налаштування системних атрибутів
resourceNotFoundUserMessage=Запитаний ресурс не знайдено. Використайте навігаційну панель або введіть правильну адресу в браузері.
responsible=Відповідальний
restore=Відновити з архіву
restoreClassMessage=Разом з класом будуть відновлені з архіву вкладені у нього типи.
restoreThis=Відновити {0} з архіву
restoreTypeMessage=Разом з типом будуть відновлені з архіву вкладені у нього типи.
restoreValues=Відновити
revision=Ревізія
run=Запустити
runItNow=Виконати зараз
save=Зберегти
savePresentation=Зберегти вигляд
script=Скрипт
search=Пошук
searchResults=Результати пошуку "{0}"
searchResultsFor=Результати пошуку:
searchRunning=виконується пошук...
second=секунд
secondShort=сек.
selectCase=Вибір типу об''єкту
selectClient=Вибір контрагенту
selectContactPerson=Вибір контактної особи
selectParent=Вибір батьківського об''єкта
selectScCase=Вибір типу запиту
selectedDifferentValues=В об''єктах встановлені різні значення
selectedSettings=Вибрані налаштування
selectedSettingsNumber=Вибрано налаштувань
sendByEmail=ВІдправити поштою
server=Сервер
serverNotAvailable=З''єднання з сервером було втрачено. Будь ласка, збережіть введені дані й перезавантажте сторінку (F5).
serverTimedOut=Превищено час очікування відповіді від сервера. Повторіть операцію пізніше.
service=Послуга
serviceCallDefaultParameters=Параметри запиту за замовчуванням
serviceTimeAllExclusionsIgnored=<br>Для даного класу обслуговування у файлі не знайдено нових вийнятків
serviceTimeExclusionsLoadComplete=Завантаження завершено
serviceTimeIgnoredEclusions=<br>Винятки, які є в класі обслуговування, ігноруються:
serviceTimeLoadedExclusions=<br>Завантажені винятки:
serviceTimeTitleForLoadResult=Клас обслуговуванння {0}
sessionChangedButton=Закрити
sessionChangedHeader=Попередження
sessionChangedMessage=Здійснено вхід до системи під іншим користувачем в іншому вікні або вкладці браузера. Дані, які відображаються на поточній сторінці, можуть бути не актуальні. Рекомендовано оновити сторінку.<br>Увага: при оновленні сторінки всі незбережені дані будуть втрачені.
sessionTimeOutAdditionalInfo=Натисніть "Залишитися на сторінці", щоб скопіювати незбережені дані, оскільки вони загубляться при повторній авторизації.\nЯкщо не потрібно зберігати дані, натисніть "Авторизуватись".
sessionTimeOutDescription=Сесія завершена.\nЗазвичай це відбувається, якщо ви тривалий час не здійснювали дій в системі або зайшли з іншого пристрою.
sessionTimeOutMessage=Операція не може бути виконана.
sessionTimeOutNoButton=Залишитись на сторінці
sessionTimeOutYesButton=Авторизуватись
setHomePageDescription=Сторінка  "{0}" буде відкриваться при кожному вході в систему та при натисканні на логотип в лівому верхньому куті. Зробити  поточну  сторінку домашньою?
settings=Налаштування
settingsParts=Групи налаштувань
show=показати
showAttrDescription=Показувати опис атрибутів
showCaption=Показувати назву
showLinkedObjects=Показувати об''єкти з двох сторін зв''язку
showMenu=Показати меню
showRelated=Показувати історію вкладених об''єктів
showRemoved=Переглянути архів
showUnRemoved=Вийти з архіву
silentModeInTestStandOn=Silent Mode (в режимі тестового стенда) ввімкнений
silentModeOff=Silent Mode вимкнено
silentModeOn=Silent Mode ввімкнено
silentModeOnWithExceptions=Silent Mode ввімкнено з вийнятками
simpleForm=Проста форма
slave=Ведений
sort=Сортування...
source=Джерело події
state=Статус
stateActive=Активний
stateDraft=Чернетка
stateOld=Стара версія
status=Стан
switchOff=Вимкнути
switchOffThis=Ввімкнути {0}
switchOn=Увімкнути
switchOnOff=Увімкнути або вимкнути
switchOnThis=Ввімкнути {0}
syncVerification=Виконувати перевірку синхронно
systemClasses=Системні класи
tab=вкладку
tabEditing=Редагування вкладки
tabNoun=вкладка
tabTitle=Назва вкладки
tabsLimitExceeded=Перевищено ліміт відкритих вкладок для {0} системи ({1}). Закрийте невикористані вкладки та оновіть сторінку.
tb=ТБ
testMetric=Перевірити
textCharacterEncoding=Кодування тексту
thereAreSavedValuesForThisForm=Для даної форми є збережені значення.
timeInterval=Проміжок часу
timeZone=Часовий пояс
timer=Лічильник часу
title=Назва
to=по
transitionTitle=назва переходу
turnOff=Вимкнути
turnOn=Увімкнути
type=тип
typeOfValue=Тип значення
typeTitleWithAttrTitle=Назва типу + "{0}"
typesOnAttrForm=Типи
underList=Під списком
unfreezeProfile=Відкріпити профіль
unit=Одиниця виміру
unload=Вивантажити
unloadingSettings=Вивантаження налаштувань
unmassCall=прибрати з підлеглих
unpin=Видалити плитку з панелі швидкого доступу
untitled=[без назви]
upContentNoun=Контент
usagePlaces=мета використання
usagePlacesOnAttrForm=Місця використання
useInContents=Використання в контентах
useInSettings=Використовується в налаштуваннях
useNameRule=Який визначається за правилом найменування
useNumberRule=Який визначається за правилом формування номера
userClasses=Користувацькі класи
userName=Ім''я користувача
value=Значення
valueMapSourceUsagePlace=Атрибут, що визначає в таблиці відповідностей
valueMapTargetUsagePlace=Атрибут, який визначається в таблиці відповідностей
valueNotSet=Не встановлено
valueVersion={0} (версія {1})
version=Версія: {0}
view=Показати
visibility=Видимість
week=тижнів
weekShort=нед.
wfProfile=Профіль пов''язаних життєвих циклів
wfTitle=Статус "{0}"
wfTitle[closed]=Закритий
wfTitle[registered]=Зареєстрований
wfTitle[reopened]=Відновлений
wfTitle[resolved]=Вирішений
wfTitle[resumed]=Відновлений
white=Білий
winInetErrors=Сталася помилка з кодом {0}
winInetErrors[12001]={0}: Неможливо згенерувати додаткові дескриптори
winInetErrors[12002]={0}: Запит прострочений
winInetErrors[12003]={0}: Сервер повернув розширену помилку. Зазвичай, це рядок або буфер, що містить повідомлення про помилку.
winInetErrors[12004]={0}: Виникла внутрішня помилка.
winInetErrors[12005]={0}: Невірна URL-адреса.
winInetErrors[12006]={0}: URL-схема не розпізнається або не підтримується.
winInetErrors[12007]={0}: Не вдалося розпізнати ім''я серверу.
winInetErrors[12008]={0}: Не вдалося знайти запитаний протокол.
winInetErrors[12009]={0}: Запит до InternetQueryOption або InternetSetOption містить недопустиме значення параметра.
winInetErrors[12010]={0}: Довжина параметра, що передається InternetQueryOption або InternetSetOption не відповідає типу вказаного параметра.
winInetErrors[12011]={0}: Параметр запиту неможливо задати, тільки запитати.
winInetErrors[12012]={0}: Підтримка функції Win32 Internet відключається або вивантажена.
winInetErrors[12016]={0}: Запитана інформація не дійсна.
winInetErrors[12017]={0}: Дія була скасована, зазвичай, це відбувається через те, що дескриптор, з яким виконувалася дія, був закритий до його завершення.
winInetErrors[12018]={0}: Не правильний тип обробника для даної дії.
winInetErrors[12019]={0}: Запитану дію не можливо виконати оскільки наданий дескриптор знаходиться не в правильному стані.
winInetErrors[12020]={0}: Запит неможливо виконати через проксі сервер.
winInetErrors[12021]={0}: Не вдалося знайти потрібне значення реєстру.
winInetErrors[12022]={0}: Потрібне значення реєстру знайдено, але має не правильний тип або не дійсне значення.
winInetErrors[12023]={0}: Наразі неможливо отримати прямий доступ до мережі.
winInetErrors[12024]={0}: Не вдалося виконати асинхронний запит, оскільки вказано нульове значення контексту.
winInetErrors[12025]={0}: Не вдалося виконати асинхронний запит, оскільки не задано функцію зворотнього виклику.
winInetErrors[12026]={0}: Не вдалося виконати дію, оскільки один або кілька запитів знаходяться в черзі.
winInetErrors[12027]={0}: Не допустимий формат запиту.
winInetErrors[12028]={0}: Не вдалося знайти запитаний об''єкт.
winInetErrors[12029]={0}: Не вдалося підключитися до серверу.
winInetErrors[12030]={0}: З''єднання з сервером було розірвано.
winInetErrors[12031]={0}: З''єднання з сервером було скинуто.
winInetErrors[12032]={0}: Викличте функцію Win32 Internet для повторного виконання запиту.
winInetErrors[12033]={0}: Недопустимий запит проксі-серверу.
winInetErrors[12036]={0}: Збій при виконані запиту: дескриптор вже існує.
winInetErrors[12037]={0}: Невірна дата SSL сертифікату, отриманого від серверу. Сертифікат прострочений.
winInetErrors[12038]={0}: Невірне загальне ім''я SSL сертифікату (поле ''Ім''я вузла''). Наприклад, було вказано ім''я серверу www.server.com, тоді як загальне ім''я сертифікату - www.different.com.
winInetErrors[12039]={0}: Програма переходить із не-SSL-з''єднання на SSL-з''єднання через перенаправлення.
winInetErrors[12040]={0}: Програма переходить з SSL з''єднання на SSL-з''єднання через переспрямування.
winInetErrors[12041]={0}: Цей код вказує на те, що вміст не достатньо захищено. Можливо, деякі дані отримані з незахищених серверів.
winInetErrors[12042]={0}: Програма відправляє дані і намагається змінити деякі рядки на незахищеному сервері.
winInetErrors[12043]={0}: Програма відправляє дані на незахищений сервер.
winInetErrors[12130]={0}: При обробці даних з серверу gopher була виявлена помилка.
winInetErrors[12131]={0}: Має бути виконаний запит для засобів пошуку файлів.
winInetErrors[12132]={0}: При отриманні даних з серверу gopher було виявлено помилку.
winInetErrors[12133]={0}: Досягнуто кінець даних.
winInetErrors[12134]={0}: Наданий вказівник невірний.
winInetErrors[12135]={0}: Невірний тип вказівника для даної операції.
winInetErrors[12136]={0}: Запитана операція може бути виконана тільки з сервером Gopher+ або вказівником на операцію Gopher+.
winInetErrors[12137]={0}: Не вдалося знайти запитаний атрибут.
winInetErrors[12138]={0}: Невідомий тип вказівника.
winInetErrors[12150]={0}: Не вдалося знайти запитаний заголовок
winInetErrors[12151]={0}: Сервер не повернув заголовок.
winInetErrors[12152]={0}: Не вдалося обробити відіповідь сервера.
winInetErrors[12153]={0}: Надано невірний заголовок.
winInetErrors[12154]={0}: Запит, надісланий до HttpQueryInfo, недійсний.
winInetErrors[12155]={0}: Неможливо додати заголовок, оскільки він вже існує.
winInetErrors[12156]={0}: Перенаправлення не вдалося, оскільки або змінилася схема (наприклад, HTTP на FTP), або всі спроби перенаправлення не вдалися (за замовчуванням - п’ять спроб).
yellow=Жовтий
yes=так
yesFirstLetterUpperCase=Так
sourceAttrCodes=Коди визначальних атрибутів
targetAttrCodes=Коди визначених атрибутів
