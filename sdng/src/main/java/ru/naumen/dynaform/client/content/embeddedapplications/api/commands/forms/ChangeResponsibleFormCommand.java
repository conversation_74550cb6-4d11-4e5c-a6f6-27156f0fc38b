package ru.naumen.dynaform.client.content.embeddedapplications.api.commands.forms;

import static ru.naumen.core.shared.Constants.EmbeddedApplicationCommand.UUID;
import static ru.naumen.metainfo.shared.elements.ContentPermissionHolderUtils.hasPermission;
import static ru.naumen.metainfo.shared.ui.Constants.EDIT_RESPONSIBLE;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.json.client.JSONObject;

import jakarta.inject.Inject;
import ru.naumen.common.client.utils.HandlerRegistrationHolder;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.ErrorAndAttentionMessageHandler;
import ru.naumen.core.client.content.toolbar.ActionHandlerRegistry;
import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.core.client.content.toolbar.ActionToolContextFactory;
import ru.naumen.core.client.content.toolbar.actions.ActionExecutedEvent;
import ru.naumen.core.client.content.toolbar.actions.ActionExecutedEventHandler;
import ru.naumen.core.client.events.FormCanceledEvent;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.Constants.HasResponsible;
import ru.naumen.core.shared.SecConstants.ServiceCall;
import ru.naumen.core.shared.attr.AggregateAttributeUtils;
import ru.naumen.core.shared.dispatch.InitContentAndObjectResponse;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.client.content.embeddedapplications.api.JsApiUtils;
import ru.naumen.dynaform.client.content.embeddedapplications.api.commands.JsApiCommandType;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.Content;

/**
 * Команда для открытия формы смены ответственного для объекта<br>
 * Параметры:
 * <ul>
 * <li>uuid - UUID объекта, для которого хочется открыть форму смены ответственного</li>
 * </ul>
 * <AUTHOR>
 * @since 18 авг. 2020 г.
 */
public class ChangeResponsibleFormCommand extends FormCommandBase implements ActionExecutedEventHandler
{
    private final MetainfoServiceSync metainfoService;
    private final CommonMessages messages;
    private final ActionToolContextFactory factory;
    private final ActionHandlerRegistry actionHandlerRegistry;
    private final JsApiUtils utils;

    @Inject
    public ChangeResponsibleFormCommand(
            final HandlerRegistrationHolder handlerRegistrationHolder,
            final MetainfoServiceSync metainfoService,
            final CommonMessages messages,
            final ActionToolContextFactory factory,
            final ActionHandlerRegistry actionHandlerRegistry,
            final JsApiUtils utils)
    {
        super(handlerRegistrationHolder);
        this.metainfoService = metainfoService;
        this.messages = messages;
        this.factory = factory;
        this.actionHandlerRegistry = actionHandlerRegistry;
        this.utils = utils;
    }

    @Override
    public JsApiCommandType getType()
    {
        return JsApiCommandType.CHANGE_RESPONSIBLE_FORM;
    }

    @Override
    public void perform(final JSONObject parameters)
    {
        final String objectUuid = Objects.requireNonNull(parameters.get(UUID).isString()).stringValue();

        utils.getFormContext(context.getParentContent(), EDIT_RESPONSIBLE, objectUuid,
                new BasicCallback<InitContentAndObjectResponse>()
                {
                    @Override
                    public void handleSuccess(InitContentAndObjectResponse response)
                    {
                        final DtObject dto = response.getDto();
                        final ClassFqn fqn = dto.getMetaClass();
                        final MetaClass metaClass = metainfoService.getMetaClass(fqn);

                        final ErrorAndAttentionMessageHandler errorHandler =
                                Objects.requireNonNull(context.getErrorAndAttentionMsgHandler());
                        if (!metaClass.hasAttribute(HasResponsible.RESPONSIBLE))
                        {
                            final MetaClass parentMetaClass = metainfoService.getMetaClass(
                                    metaClass.getFqn().fqnOfClass());
                            // у класса объекта нет назначения ответственного
                            errorHandler.addErrorMessage(
                                    messages.hasNoResponsible(dto.getTitle(), parentMetaClass.getTitle()));
                            return;
                        }
                        final PermissionHolder permissions = response.getPermissions();
                        if (!hasAnyPermission(response.getContent(), permissions))
                        {
                            errorHandler.addErrorMessage(
                                    messages.cannotChangeResponsible(dto.getTitle(), metaClass.getTitle()));
                            return;
                        }

                        final EventBus eventBus = context.getEventBus();
                        handlerRegistrationHolder.register(
                                eventBus.addHandler(ActionExecutedEvent.getType(), ChangeResponsibleFormCommand.this));
                        handlerRegistrationHolder.register(
                                eventBus.addHandler(FormCanceledEvent.getType(), ChangeResponsibleFormCommand.this));

                        context.setMetainfo(metaClass, false);
                        context.setObject(dto, false);
                        context.setPermissions(permissions);

                        final ActionToolContext actionToolContext =
                                factory.create(response.getContent(), context, null, EDIT_RESPONSIBLE);
                        actionHandlerRegistry.getHandler(EDIT_RESPONSIBLE, actionToolContext).execute();
                    }
                });
    }

    /**
     * Срабатывает при сохранении формы
     *
     * @param event событие
     */
    @Override
    public void onActionExecutedEvent(final ActionExecutedEvent event)
    {
        final Object resultValue = event.getResultValue();
        if (!(resultValue instanceof DtObject))
        {
            return;
        }

        final DtObject responsible = (DtObject)resultValue;
        final List<String> uuidList = new ArrayList<>(2);
        uuidList.add(AggregateAttributeUtils.getTeamUuid(responsible));
        final String employeeUuid = AggregateAttributeUtils.getEmployeeUuid(responsible);
        if (employeeUuid != null)
        {
            uuidList.add(employeeUuid);
        }

        handlerRegistrationHolder.removeHandlers();
        sendFormCommandResponse(HasResponsible.RESPONSIBLE, uuidList);
    }

    private static boolean hasAnyPermission(final Content content, final PermissionHolder permissionHolder)
    {
        return ServiceCall.RESPONSIBLE_MARKERS.stream()
                .anyMatch(permissionKey -> hasPermission(permissionHolder, permissionKey, content));
    }
}
