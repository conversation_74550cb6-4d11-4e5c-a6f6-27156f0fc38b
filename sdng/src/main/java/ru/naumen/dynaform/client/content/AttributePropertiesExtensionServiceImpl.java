package ru.naumen.dynaform.client.content;

import java.util.ArrayList;
import java.util.List;

import jakarta.inject.Singleton;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.client.DynaContext;
import ru.naumen.dynaform.client.actioncommand.FormContext;
import ru.naumen.dynaform.client.actioncommand.PropertyListContext;
import ru.naumen.dynaform.client.events.FieldChangedEvent;
import ru.naumen.dynaform.shared.masseditform.BlockAttributeInfo;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Реализация сервиса расширений для определения видимого списка атрибутов.
 * <AUTHOR>
 * @since Aug 21, 2022
 */
@Singleton
public class AttributePropertiesExtensionServiceImpl implements AttributePropertiesExtensionService
{
    private final List<AttributePropertiesExtension> extensions = new ArrayList<>();

    @Override
    public void initializeOnForm(PropertyListContext context)
    {
        context.getReadyState().ready(new ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                extensions.forEach(extension -> extension.initializeOnForm(context));
            }
        });
    }

    @Override
    public List<Attribute> getVisibleAttributes(List<Attribute> attributes, DynaContext context, boolean edit)
    {
        List<Attribute> result = attributes;
        for (AttributePropertiesExtension extension : extensions)
        {
            result = extension.getVisibleAttributes(result, context, edit);
        }
        return result;
    }

    @Override
    public List<Attribute> getVisibleAttributes(List<Attribute> attributes, DtObject object, boolean edit)
    {
        List<Attribute> result = attributes;
        for (AttributePropertiesExtension extension : extensions)
        {
            result = extension.getVisibleAttributes(result, object, edit);
        }
        return result;
    }

    @Override
    public List<BlockAttributeInfo> getVisibleBlockAttributes(List<BlockAttributeInfo> attributes, DynaContext context)
    {
        List<BlockAttributeInfo> result = attributes;
        for (AttributePropertiesExtension extension : extensions)
        {
            result = extension.getVisibleBlockAttributes(result, context);
        }
        return result;
    }

    @Override
    public List<BlockAttributeInfo> getVisibleBlockAttributes(List<BlockAttributeInfo> attributes, DtObject object)
    {
        List<BlockAttributeInfo> result = attributes;
        for (AttributePropertiesExtension extension : extensions)
        {
            result = extension.getVisibleBlockAttributes(result, object);
        }
        return result;
    }

    @Override
    public void onFieldChanged(FieldChangedEvent event)
    {
        extensions.forEach(extension -> extension.onFieldChanged(event));
    }

    @Override
    public boolean registerAttribute(FormContext context, Attribute attribute)
    {
        if (extensions.stream().noneMatch(extension -> extension.registerAttribute(context, attribute)))
        {
            context.registerAttribute(attribute);
        }
        return true;
    }

    @Override
    public void registerExtension(AttributePropertiesExtension extension)
    {
        extensions.add(extension);
    }
}
