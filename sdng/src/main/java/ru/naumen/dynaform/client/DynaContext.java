package ru.naumen.dynaform.client;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.shared.HasReadyState;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ui.Content;

/**
 * Контекст формы (карточки объекта) в интерфейсе оператора
 *
 * <AUTHOR>
 *
 */
public interface DynaContext extends Context, HasReadyState
{
    List<Long> getEditingSessionIds();

    String getFormCode();

    List<ClassFqn> getMetaClassesFqn();

    /**
     * Возвращает первый из {@link #getObjects() отображаемых объектов}
     */
    @CheckForNull
    DtObject getObject();

    /**
     * Возвращает отображаемые объекты.
     */
    Set<DtObject> getObjects();

    @Nullable
    <R> R getPermissionMetaData(String key);

    boolean isContentVisible(String contentUuid);

    default boolean isContentVisible(Content content)
    {
        return isContentVisible(content.getUuid());
    }

    void setContentVisible(String contentUuid, boolean visible);

    default void setContentVisible(Content content, boolean visible)
    {
        setContentVisible(content.getUuid(), visible);
    }

    /**
     * Устанавливает объекты
     */
    void setObjects(Collection<DtObject> objects);
}
