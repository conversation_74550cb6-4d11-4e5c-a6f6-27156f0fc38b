package ru.naumen.dynaform.client;

import static ru.naumen.core.shared.utils.CommonUtils.METAINFO;
import static ru.naumen.core.shared.utils.CommonUtils.assertNotNull;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.google.common.collect.ImmutableList;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.event.shared.SimpleEventBus;

import jakarta.annotation.Nullable;
import ru.naumen.core.client.content.AbstractContext;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.ErrorAndAttentionMessageHandler;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Реализация {@link DynaContext}
 *
 * <AUTHOR>
 * @since 05.10.2010
 *
 */
public class DefaultDynaContext extends AbstractContext implements ChildDynaContext
{
    private Set<DtObject> objects;
    private String formCode;
    private final List<Long> editingSessionIds = new ArrayList<>();

    private Map<String, Boolean> contentVisibility = new HashMap<>();

    public DefaultDynaContext(@Nullable Context parentContext, @Nullable MetaClass metaClass, @Nullable DtObject obj)
    {
        this(parentContext, metaClass, Collections.singleton(obj));
    }

    public DefaultDynaContext(@Nullable Context parentContext, @Nullable MetaClass metaClass,
            Collection<DtObject> objects)
    {
        this(parentContext, metaClass, objects, new SimpleEventBus(), null);
    }

    public DefaultDynaContext(@Nullable Context parentContext, @Nullable MetaClass metaClass, @Nullable DtObject obj,
            EventBus eventBus)
    {
        this(parentContext, metaClass, Collections.singleton(obj), eventBus,
                parentContext == null ? null : parentContext.getErrorAndAttentionMsgHandler());
    }

    public DefaultDynaContext(@Nullable Context parentContext, @Nullable MetaClass metaClass,
            Collection<DtObject> objects, EventBus eventBus,
            @Nullable ErrorAndAttentionMessageHandler errorMessageHandler)
    {
        super(parentContext, metaClass, eventBus, errorMessageHandler);
        this.objects = new LinkedHashSet<>(objects);
    }

    public DefaultDynaContext(Collection<DtObject> objects)
    {
        this(null, null, objects);
    }

    public DefaultDynaContext(DynaContext parentContext, EventBus eventBus)
    {
        this(parentContext, parentContext.getMetainfo(), parentContext.getObject(), eventBus);
    }

    public DefaultDynaContext(@Nullable MetaClass metaClass, DtObject obj, @Nullable PermissionHolder permissions)
    {
        this(null, metaClass, Collections.singleton(obj));
        this.setPermissions(permissions);
    }

    @Override
    public List<Long> getEditingSessionIds()
    {
        return editingSessionIds;
    }

    @Override
    public String getFormCode()
    {
        return formCode;
    }

    @Override
    public List<ClassFqn> getMetaClassesFqn()
    {
        MetaClass metainfo = getMetainfo();
        assertNotNull(metainfo, METAINFO);
        return ImmutableList.of(metainfo.getFqn());
    }

    @Nullable
    @Override
    public DtObject getObject()
    {
        return objects.isEmpty() ? null : objects.iterator().next();
    }

    @Override
    public Set<DtObject> getObjects()
    {
        return objects;
    }

    @Nullable
    @Override
    @SuppressWarnings("unchecked")
    public DynaContext getParentContext()
    {
        return (DynaContext)super.getParentContext();
    }

    @Override
    @Nullable
    public <R> R getPermissionMetaData(String key)
    {
        if (null != getPermissions())
        {
            return getPermissions().getPermissionMetaData(key);
        }
        DynaContext parentContext = getParentContext();
        if (null != parentContext)
        {
            return parentContext.getPermissionMetaData(key);
        }
        return null;
    }

    @Override
    public boolean isContentVisible(String contentUuid)
    {
        return !Boolean.FALSE.equals(contentVisibility.get(contentUuid));
    }

    @Override
    public void setContentVisible(String contentUuid, boolean visible)
    {
        contentVisibility.put(contentUuid, visible);
    }

    public void setFormCode(@Nullable String formCode)
    {
        this.formCode = formCode;
    }

    public void setObject(DtObject object)
    {
        setObject(object, true);
    }

    public void setObject(@Nullable DtObject object, boolean fireEvent)
    {
        setObjects(Collections.singleton(object), fireEvent);
    }

    @Override
    public void setObjects(Collection<DtObject> objects)
    {
        setObjects(objects, false);
    }

    public void setObjects(Collection<DtObject> objects, boolean fireEvent)
    {
        this.objects = new LinkedHashSet<>(objects);
        if (fireEvent)
        {
            fireContextChanged();
        }
    }

    @Override
    protected void setParentContext(@Nullable Context parent)
    {
        super.setParentContext(parent);
        if (parent instanceof DefaultDynaContext)
        {
            this.contentVisibility = ((DefaultDynaContext)parent).contentVisibility;
        }
        setFormCode(parent instanceof DynaContext ? ((DynaContext)parent).getFormCode() : UI.WINDOW_KEY);
    }

    protected boolean isValueNotChanged(String attributeCode, @Nullable Object value)
    {
        Object oldValue = Objects.requireNonNull(getObject()).getProperty(attributeCode);
        return Objects.equals(oldValue, value);
    }
}
