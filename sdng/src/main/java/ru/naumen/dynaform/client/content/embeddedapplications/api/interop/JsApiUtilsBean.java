package ru.naumen.dynaform.client.content.embeddedapplications.api.interop;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

import com.google.inject.Inject;

import elemental2.promise.Promise;
import jakarta.annotation.Nullable;
import jsinterop.annotations.JsMethod;
import jsinterop.annotations.JsOptional;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.common.RequestEditObjectDefinition;
import ru.naumen.core.client.common.RequestObjectDefinition;
import ru.naumen.core.client.jsinterop.JsArray;
import ru.naumen.core.client.jsinterop.JsObject;
import ru.naumen.core.client.jsinterop.jsapi.UtilsParams;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.DtoCriteriaHelper;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.criteria.Order;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.dynaform.client.DynaformObjectService;
import ru.naumen.dynaform.client.content.embeddedapplications.api.JsConverter;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Класс, реализующий методы работы с объектами для jsApi
 *
 * <AUTHOR>
 * @since March 25, 2021
 */
public class JsApiUtilsBean
{
    private final DynaformObjectService objectService;
    private final MetainfoServiceSync metainfoServiceSync;
    private final DtoCriteriaHelper dtoCriteriaHelper;

    @Inject
    public JsApiUtilsBean(final DynaformObjectService objectService,
            final MetainfoServiceSync metainfoServiceSync,
            final DtoCriteriaHelper dtoCriteriaHelper)
    {
        this.objectService = objectService;
        this.metainfoServiceSync = metainfoServiceSync;
        this.dtoCriteriaHelper = dtoCriteriaHelper;
    }

    /**
     * Позволяет определить опциональные параметры для поиска объектов
     */
    @SuppressWarnings("unused") // используется в jsApi,utils
    @JsMethod
    public UtilsParams buildParams()
    {
        return new UtilsParams();
    }

    /**
     * Производит поиск объектов по значению атрибутов
     *
     * @param fqn тип искомых объектов
     * @param attributes карта значений <код атрибута, значение атрибута>
     * @param params объект, хранящий в себе дополнительные параметры поиска (offset, limit, ignoreCase, attrs)
     * @return {@link Promise} с коллекцией объектов, значения атрибутов которых соответствуют {@code attributes},
     * учитывая {@code searchParams}
     */
    @JsMethod
    public Promise<JsArray<JsObject>> find(String fqn, JsObject attributes, @JsOptional @Nullable UtilsParams params)
    {
        final DtoCriteria dtoCriteria = getDtoCriteria(fqn, attributes, params);

        return new Promise<>((resolve, reject) -> objectService.getDtObjectListJsApiResponse(dtoCriteria, null,
                new ArrayList<>(), true, false, new DefaultErrorHandlingCallback<>(reject, response ->
                {
                    final JsArray<JsObject> result = new JsArray<>();
                    response.getObjects()
                            .stream()
                            .map(dto -> JsConverter.convertToPlain(metainfoServiceSync.getMetaClass(dto.getMetainfo()),
                                    dto))
                            .forEach(result::push);

                    resolve.onInvoke(result);
                })));
    }

    /**
     * Производит поиск объектов по значению атрибутов и возвращает первый найденный объект,
     * либо null, если ничего не нашел
     *
     * @param fqn nbg искомых объектов
     * @param attributes карта значений <код атрибута, значение атрибута>
     * @return {@link Promise} либо с первым найденным объектом, либо с null, если ничего не нашел
     */
    @JsMethod
    public Promise<JsObject> findFirst(String fqn, JsObject attributes, @JsOptional @Nullable UtilsParams params)
    {
        return find(fqn, attributes, ((params != null) ? params : new UtilsParams()).limit(1))
                .then(array -> Promise.resolve(array.length > 0 ? array.pop() : null));
    }

    /**
     * Изменяет существующий бизнес-объект
     *
     * @param uuid идентификатор изменяемого объекта
     * @param attributes карта значений изменяемых атрибутов объекта <код атрибута, значение атрибута>
     * @return {@link Promise} содержащий измененный объект
     */
    @JsMethod
    public Promise<JsObject> edit(String uuid, JsObject attributes, @JsOptional @Nullable UtilsParams params)
    {
        final DtObject dto = new SimpleDtObject(uuid, null);
        final MapProperties properties = getProperties(attributes);
        final DtoProperties resultProperties = getResultProperties(params);

        return new Promise<>((resolve, reject) ->
        {
            RequestEditObjectDefinition definition = new RequestEditObjectDefinition()
                    .setObj(dto)
                    .setProperties(properties)
                    .setResultProperties(resultProperties)
                    .setFormCode(UI.JS_API)
                    .setFireEvents(true)
                    .setCallback(new JsApiTransformationCallback<>(metainfoServiceSync, resolve, reject));
            objectService.editObject(definition);
        });
    }

    /**
     * Создает новый бизнес-объект
     *
     * @param fqn код типа создаваемого объекта
     * @param attributes карта значений атрибутов создаваемого объекта <код атрибута, значение атрибута>
     * @return {@link Promise} содержащий созданный объект
     */
    @JsMethod
    public Promise<JsObject> create(String fqn, JsObject attributes, @JsOptional @Nullable UtilsParams params)
    {
        final ClassFqn objectFqn = ClassFqn.parse(fqn);
        final DtoProperties resultProperties = getResultProperties(params);

        final MapProperties properties = getProperties(attributes);
        properties.put(AbstractBO.METACLASS, objectFqn);
        return new Promise<>((resolve, reject) -> objectService.addObject(objectFqn, properties, resultProperties, null,
                new JsApiTransformationCallback<>(metainfoServiceSync, resolve, reject)));
    }

    /**
     * Получение объекта по его идентификатору
     *
     * @param uuid идентификатор объекта
     * @return {@link Promise} содержащий объект
     */
    @JsMethod
    public Promise<JsObject> get(String uuid, @JsOptional @Nullable UtilsParams params)
    {
        final DtoProperties resultProperties = getResultProperties(params);
        return new Promise<>((resolve, reject) ->
        {
            RequestObjectDefinition requestObjectDefinition = new RequestObjectDefinition(uuid)
                    .setProperties(resultProperties)
                    .setCallback(new JsApiTransformationCallback<>(metainfoServiceSync, resolve, reject));
            objectService.getObjectForJsApi(requestObjectDefinition);
        });
    }

    /**
     * Удаляет бизнес-объект
     *
     * @param uuid индикатор удаляемого бизнес-объекта
     * @return {@link Promise} содержащий индикатор удаленного бизнес-объекта
     */
    @JsMethod
    public Promise<String> delete(String uuid)
    {
        final DtObject dto = new SimpleDtObject(uuid, null);

        return new Promise<>((resolve, reject) ->
                objectService.deleteObject(dto,
                        new DefaultErrorHandlingCallback<>(reject, result -> resolve.onInvoke(uuid))));
    }

    private DtoCriteria getDtoCriteria(String fqn, JsObject attributes, @Nullable UtilsParams params)
    {
        final ClassFqn classFqn = ClassFqn.parse(fqn);
        final MetaClass metaClass = metainfoServiceSync.getMetaClass(classFqn);
        // если метакласс не существует, то формируем пустую DtoCriteria,
        // в таком случае сервер корректно обработает данный кейс
        if (metaClass == null)
        {
            return new DtoCriteria(classFqn);
        }

        final MapProperties properties = getProperties(attributes);

        DtoCriteria dtoCriteria;
        if (params != null)
        {
            dtoCriteria = dtoCriteriaHelper.getDtoCriteria(metaClass, properties, params.getIgnoreCase());
            dtoCriteria.setFirstResult(Optional.ofNullable(params.getOffset()).map(Number::intValue).orElse(0));
            dtoCriteria.setMaxResults(
                    Optional.ofNullable(params.getLimit()).map(Number::intValue).orElse(DtoCriteria.NO_LIMIT));
        }
        else
        {
            dtoCriteria = dtoCriteriaHelper.getDtoCriteria(metaClass, properties, false);
            dtoCriteria.setFirstResult(0);
            dtoCriteria.setMaxResults(DtoCriteria.NO_LIMIT);
        }

        if (metaClass.hasAttribute(Constants.AbstractBO.CREATION_DATE))
        {
            dtoCriteria.setOrders(Collections.singletonList(new Order(Constants.AbstractBO.CREATION_DATE, false)));
        }

        final Set<String> attributeCodes = getAttributeCodes(params);
        if (attributeCodes != null)
        {
            dtoCriteria.setProperties(attributeCodes);
        }

        return dtoCriteria;
    }

    @Nullable
    private static DtoProperties getResultProperties(@Nullable UtilsParams params)
    {
        final Set<String> attributeCodes = getAttributeCodes(params);

        return (attributeCodes != null) ? new DtoProperties(null, attributeCodes) : null;
    }

    @Nullable
    private static Set<String> getAttributeCodes(@Nullable UtilsParams params)
    {
        final JsArray<String> attributeCodes = params != null ? params.getAttributeCodes() : null;
        if (attributeCodes != null && attributeCodes.length > 0)
        {
            final Set<String> propertiesCodes = new HashSet<>(attributeCodes.length);
            attributeCodes.forEach((item, i) -> propertiesCodes.add(item));

            return propertiesCodes;
        }

        return null; //NOSONAR
    }

    private static MapProperties getProperties(JsObject object)
    {
        final MapProperties properties = new MapProperties();
        for (String property : object.properties())
        {
            properties.setProperty(property, JsConverter.convertFromPlain(object.get(property)));
        }
        return properties;
    }
}
