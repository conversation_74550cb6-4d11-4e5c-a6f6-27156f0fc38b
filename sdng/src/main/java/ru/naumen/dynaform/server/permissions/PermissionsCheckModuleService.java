package ru.naumen.dynaform.server.permissions;

import java.util.List;

import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.elements.sec.Profile;
import ru.naumen.sec.server.autorize.AuthorizationInfo;

/**
 * Сервис модуля проверки прав на серверной стороне
 *
 * <AUTHOR>
 * @since 10 июня 2016 г.
 */
public interface PermissionsCheckModuleService
{
    /**
     * Получить профили сотрудника
     * @param authInfo данные авторизации
     */
    List<Profile> getProfiles(AuthorizationInfo authInfo);

    /**
     * Получить {@link AuthorizationInfo} по uuid сотрудника
     * @param uuid уникальный идентификатор сотрудника
     */
    AuthorizationInfo getAuthInfo(String uuid);

    /**
     * Получить текущего сотрудника в виде {@link DtObject}.
     * @param employee uuid сотрудника
     */
    DtObject getCurrentEmployee(IUUIDIdentifiable employee);

    /**
     * Получить группы пользователя
     * @param employee
     */
    List<String> getGroups(Employee employee);

    /**
     * Получить лицензии сотрудника.
     * @param employee сотрудник,
     */
    List<String> getLicenses(Employee employee);
}
