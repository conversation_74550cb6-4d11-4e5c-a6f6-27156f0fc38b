package ru.naumen.dynaform.server.permissions;

import static ru.naumen.core.shared.Constants.Employee.ALL_GROUP;
import static ru.naumen.core.shared.Constants.Employee.IS_CURRENT_USER_HAS_SUPER_USER_OPERATOR_RIGHTS;
import static ru.naumen.core.shared.Constants.Employee.NOT_LICENSED_USER;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.server.autorize.GrantedPermission;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.common.AccessorHelper;
import ru.naumen.core.server.license.LicensingService;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.dto.SimpleTreeDtObject;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.ui.ProfilesExtractorStrategy;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.sec.Group;
import ru.naumen.metainfo.shared.elements.sec.Profile;
import ru.naumen.sec.server.autorize.AuthorizationInfo;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;
import ru.naumen.sec.server.autorize.AuthorizationService;
import ru.naumen.sec.server.autorize.cache.AuthorizeUserCacheService;
import ru.naumen.sec.server.users.CurrentEmployeeContext;
import ru.naumen.sec.server.users.UsersService;

/**
 * Реализация {@link PermissionsCheckModuleService}.
 *
 * <AUTHOR>
 * @since 10 июня 2016 г.
 */
@Component
public class PermissionsCheckModuleServiceImpl implements PermissionsCheckModuleService
{
    private final CurrentEmployeeContext currentEmployeeContext;
    private final IPrefixObjectLoaderService loaderService;
    private final MappingService mappingService;
    private final AuthorizationService authorizationService;
    private final LicensingService licensingService;
    private final MessageFacade messages;
    private final ProfilesExtractorStrategy profilesExtractorStrategies;
    private final UsersService usersService;

    @Inject
    public PermissionsCheckModuleServiceImpl(
            CurrentEmployeeContext currentEmployeeContext,
            IPrefixObjectLoaderService loaderService,
            MappingService mappingService,
            AuthorizationService authorizationService,
            LicensingService licensingService,
            MessageFacade messages,
            ProfilesExtractorStrategy profilesExtractorStrategies,
            UsersService usersService)
    {
        this.currentEmployeeContext = currentEmployeeContext;
        this.loaderService = loaderService;
        this.mappingService = mappingService;
        this.authorizationService = authorizationService;
        this.licensingService = licensingService;
        this.messages = messages;
        this.profilesExtractorStrategies = profilesExtractorStrategies;
        this.usersService = usersService;
    }

    @Override
    public List<Profile> getProfiles(AuthorizationInfo authInfo)
    {
        return profilesExtractorStrategies.getProfiles(authInfo);
    }

    @Override
    public AuthorizationInfo getAuthInfo(String uuid)
    {
        IHasMetaInfo object = loaderService.get(uuid);
        return authorizationService.getAuthorizationInfo(object.getMetaClass(), object, IProperties.EMPTY);
    }

    @Override
    public DtObject getCurrentEmployee(IUUIDIdentifiable employee)
    {
        DtObject dto = new SimpleTreeDtObject(null, new SimpleDtObject());
        mappingService.transform(employee, dto, new DtoProperties(null,
                Set.of(Constants.AbstractBO.UUID, Constants.AbstractBO.TITLE, Constants.AbstractBO.METACLASS)));
        dto.setProperty(IS_CURRENT_USER_HAS_SUPER_USER_OPERATOR_RIGHTS,
                currentEmployeeContext.isCurrentUserHasSuperUserOperatorRights());
        return dto;
    }

    @Override
    public List<String> getGroups(Employee employee)
    {
//        Employee currentEmployee = authUserCacheService.getCurrentEmployee();
        return usersService.getUserGroups(employee).stream().map(Group::getCode).toList();
    }

    @Override
    public List<String> getLicenses(Employee employee)
    {
        Set<String> codes = employee.getLicense();
        if (codes == null || codes.isEmpty() || codes.contains(NOT_LICENSED_USER))
        {
            return List.of(messages.getMessage("pc.module.unlicensed"));
        }
        return licensingService.getLicenseGroups().stream()
                .filter(group -> codes.contains(group.getId()))
                .map(group -> !isEmptyTrim(group.getTitle()) ? group.getTitle() : group.getId())
                .sorted(String.CASE_INSENSITIVE_ORDER)
                .toList();
    }

    private boolean isEmptyTrim(@Nullable String str)
    {
        return str == null || str.trim().isEmpty();
    }
}
