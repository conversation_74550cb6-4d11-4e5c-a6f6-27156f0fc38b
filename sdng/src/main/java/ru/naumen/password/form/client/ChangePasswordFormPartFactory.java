package ru.naumen.password.form.client;

import jakarta.annotation.Nullable;
import ru.naumen.core.client.widgets.HasProperties;

/**
 * Фабрика {@link ChangePasswordFormPart}
 * <AUTHOR>
 * @since 18 нояб. 2015 г.
 */
public interface ChangePasswordFormPartFactory
{
    /**
     * Создать {@link ChangePasswordFormPart}
     * @param display display на котором будут размещены свойства
     * @param userUuid uuid пользователя для котрого выполняется смена пароля (может быть null)
     * @param needCurrentPassword нужен ли запрос текущего пароля
     * @return презентер со свойствами для смены пароля
     */
    ChangePasswordFormPart create(HasProperties display, @Nullable String userUuid, boolean needCurrentPassword);
}
