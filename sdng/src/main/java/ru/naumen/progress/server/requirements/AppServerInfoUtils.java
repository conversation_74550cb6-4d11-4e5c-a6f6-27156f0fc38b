package ru.naumen.progress.server.requirements;

import ru.naumen.core.shared.Constants;

/**
 * Вспомогательные методы для получения информации о сервере приложений, на котором запущена SMP
 *
 * <AUTHOR>
 * @since 01.02.2021
 */
public final class AppServerInfoUtils
{
    private AppServerInfoUtils()
    {
    }

    /**
     * @return тип сервера приложений, на котором запускается SMP
     */
    public static String getApplicationServer()
    {
        return Constants.TOMCAT;
    }
}
