package ru.naumen.advimport.client.admin.config.commands;

import java.util.Collection;
import java.util.stream.Collectors;

import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.advimport.client.admin.AdvImportMessages;
import ru.naumen.advimport.shared.dispatch.DelConfigurationAction;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.commands.ObjectCommandImpl;

/**
 * Команда удаления конфигурации импорта
 * <AUTHOR>
 * @since 03.10.2017
 */
public class DelConfigCommand extends ObjectCommandImpl<Collection<DtObject>, Void>
{
    @Inject
    private AdvImportMessages messages;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private DispatchAsync dispatch;

    @Inject
    public DelConfigCommand(@Assisted CommandParam<Collection<DtObject>, Void> param, Dialogs dialogs)
    {
        super(param, dialogs);
    }

    @Override
    protected String getDialogMessage(Collection<DtObject> values)
    {
        if (values.size() == 1)
        {
            return cmessages.confirmDeleteQuestion(messages.configuration(), values.iterator().next().getTitle());
        }
        return cmessages.confirmDeleteQuestion2(messages.configurationsSelected());
    }

    @Override
    protected String getDialogTitle()
    {
        return cmessages.confirmDelete();
    }

    @Override
    protected String getIconCode()
    {
        return null;
    }

    @Override
    protected void onDialogSuccess(CommandParam<Collection<DtObject>, Void> param)
    {
        dispatch.execute(
                new DelConfigurationAction(
                        param.getValue().stream().map(IUUIDIdentifiable::getUUID).collect(Collectors.toList())),
                new CallbackDecorator<SimpleResult<String>, Void>(param.getCallback())
                {
                    @Override
                    public void onSuccess(SimpleResult<String> result)
                    {
                        if (null == result.get())
                        {
                            super.onSuccess(result);
                        }
                        else
                        {
                            onFailure(new FxException(result.get()));
                        }
                    }

                    @Override
                    protected Void apply(SimpleResult<String> from)
                    {
                        return null;
                    }
                });
    }
}