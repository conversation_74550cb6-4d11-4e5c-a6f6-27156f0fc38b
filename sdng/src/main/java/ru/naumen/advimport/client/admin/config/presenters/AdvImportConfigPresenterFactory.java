package ru.naumen.advimport.client.admin.config.presenters;

import jakarta.annotation.Nullable;

import ru.naumen.advimport.shared.ImportConfigContainer;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.dto.DtoContainer;

import com.google.gwt.event.shared.EventBus;

public interface AdvImportConfigPresenterFactory<T extends Presenter>
{
    T create(@Nullable DtoContainer<ImportConfigContainer> cnt, EventBus presenterEventBus);
}