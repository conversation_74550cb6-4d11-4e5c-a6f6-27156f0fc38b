/**
 *
 */
package ru.naumen.core.client.css;

import com.google.gwt.resources.client.CssResource;

/**
 * <AUTHOR>
 *
 */
public interface AllCss extends CssResource
{
    @ClassName("actionBarDisplay")
    String actionBarDisplay();

    /**
     * CSS-класс-маркер, устанавливаемый на body, означающий, что все действия (кнопки) без
     * стиля {@link #actionsForceEnabled()} должны отображаться как disabled. Клики по ним
     * по-хорошему должны отменяться.
     * Единственный случай использования на данный момент - сворачивание модальных окон,
     * и там клики действительно отменяются.
     */
    String actionsDisabled();

    /**
     * CSS-класс-маркер, используемый в паре с {@link #actionsDisabled()}, накладываемый на конкретные
     * элементы вёрстки, клики по которым должны быть разрешены.
     * Обычно это действия, которые меняют визуальное представление у пользователя, но не приводят
     * к изменению бизнес-данных.
     */
    String actionsForceEnabled();

    @ClassName("add-object-action")
    String addObjectAction();

    @ClassName("advListPanel")
    String advListPanel();

    @ClassName("alignCenter")
    String alignCenter();

    @ClassName("alignLeft")
    String alignLeft();

    @ClassName("AttributeGroup")
    String attributeGroup();

    String attributeGroupCaption();

    @ClassName("AttributeGroups")
    String attributeGroups();

    @ClassName("AttributeGroupsTitle")
    String attributeGroupsTitle();

    @ClassName("Attributes")
    String attributes();

    @Deprecated
    String blockTitle();

    @ClassName("b-col-content_popup")
    String bColContentPopup();

    @ClassName("b-col-content_popup_header")
    String bColContentPopupHeader();

    @ClassName("black-and-white")
    String blackAndWhite();

    String breakWord();

    String clearableTextBox();

    String clearTextBoxIcon();

    String colorCircleContainer();

    String colorCircle();

    String colorCaption();

    @ClassName("compact-buttons")
    String compactButtons();

    @ClassName("copy-from-parent")
    String copyFromParent();

    @ClassName("copy-from-template")
    String copyFromTemplate();

    @ClassName("disabled")
    String disabled();

    @ClassName("disabledItem")
    String disabledItem();

    @ClassName("eventFilterTool")
    String eventFilterTool();

    @ClassName("exclusionPanel")
    String exclusionPanel();

    String flex();

    @ClassName("flex-generous")
    String flexGenerous();

    @ClassName("flex-greedy")
    String flexGreedy();

    @ClassName("flex-vertical")
    String flexVertical();

    @ClassName("floatLeft")
    String floatLeft();

    @ClassName("floatRight")
    String floatRight();

    @ClassName("font-bold")
    String fontBold();

    @ClassName("g-buttons-admin-advlist")
    String gButtonsAdminAdvlist();

    @ClassName("g-h3")
    String gh3();

    @ClassName("g-h4")
    String gh4();

    @ClassName("g-nw")
    String gNW();

    @ClassName("g-popup")
    String gPopup();

    @ClassName("g-popup__close")
    String gPopupClose();

    @ClassName("g-popup_help")
    String gPopupHelp();

    @ClassName("Groups")
    String groups();

    @ClassName("gwt-Label")
    String gwtLabel();

    @ClassName("gwt-RichTextArea")
    String gwtRichTextArea();

    @ClassName("hideComponent")
    String hideComponent();

    @ClassName("icon-title-text")
    String iconTitleText();

    @ClassName("inline-block")
    String inlineBlock();

    @ClassName("listBox")
    String listBox();

    String mainContentPanel();

    String mainTabPanel();

    /**
     * Возвращает универсально используемый (не привязанный к конкретному тегу) css-стиль,
     * отменяющий объединение подряд идущих пробелов в тексте
     *
     * @return
     */
    @ClassName("no-whitespace-collapse")
    String noWhitespaceCollapse();

    @ClassName("onNamingHelp")
    String onNamingHelp();

    String overflowHidden();

    @ClassName("periodPanel")
    String periodPanel();

    @ClassName("pointer")
    String pointer();

    /**
     * Маркер элемента, переведенного в состояние hover програмно
     */
    String hover();

    /**
     * Маркер элемента, переведенного в состояние active програмно
     */
    String active();

    String pullRight();

    String pullRightUp();

    @ClassName("refreshToolBarIcon")
    String refreshToolBarIcon();

    @ClassName("removedObjectLink")
    String removedObjectLink();

    @ClassName("richText")
    String richText();

    @ClassName("richTextUnsafeView")
    String richTextUnsafeView();

    @ClassName("Roles")
    String roles();

    @ClassName("SchedulerTasksDisplay")
    String schedulerTasksDisplay();

    @ClassName("SecurityDisplayImpl")
    String securityDisplayImpl();

    String selectedItem();

    @ClassName("showUsageAttrGroupIcon")
    String showUsageAttrGroupIcon();

    @ClassName("stopWindowScroll")
    String stopWindowScroll();

    @ClassName("tableContainer")
    String tableContainer();

    @ClassName("TableDisplayImpl")
    String tableDisplayImpl();

    @ClassName("tableDisplayInfo")
    String tableDisplayInfo();

    @ClassName("TakeContainer")
    String takeContainer();

    String templateMode();

    @ClassName("TextButtonBoxClearButton")
    String textButtonBoxClearButton();

    @ClassName("TextButtonBoxSearchButton")
    String textButtonBoxSearchButton();

    @ClassName("TextButtonBoxText")
    String textButtonBoxText();

    @ClassName("tool-panel")
    String toolPanel();

    String toolPanelIsInsideUIDisplayContent();

    @ClassName("TriggersDisplay")
    String triggersDisplay();

    String warningItem();

    /**
     * Таббар на всю строку. Следующий - с новой строки.
     */
    String wholeLine();

    @ClassName("widget-panel")
    String widgetPanel();

    String toolPanelIsInsideTab();

    /**
     * Кнопки настройки контентов. Располагаются над вкладками в админке, в панели вкладок
     */
    String toolPanelContentSettings();

    String objectCard();

    /**
     * Делает "неактивными" иконки, являющиеся дочерними по отношению к данному элементу
     */
    @ClassName("disabledFontIconContainer")
    String disabledFontIconContainer();

    /**
     * Задает многострочность для элемента.
     */
    String multiLine();

    /**
     * Задает отступ для списка
     */
    String titlePanel();

    /**
     * Задает стиль выключенных, но выбираемых элементов
     */
    String disabledSelectableItem();

    /**
     * Текст письма
     */
    String mailTextBody();

    /**
     * Заголовки письма
     */
    String mailHeaders();
}
