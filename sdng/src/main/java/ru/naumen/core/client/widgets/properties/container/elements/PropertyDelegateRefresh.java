package ru.naumen.core.client.widgets.properties.container.elements;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;

/**
 * Делегат контроллера свойства, вызываемый при его обновлении
 *
 * @param <T> тип значения, вводимого в поле
 * @param <P> тип виджета обновляемого поля
 *
 * <AUTHOR>
 * @since 17.05.2012
 */
public interface PropertyDelegateRefresh<T, P extends Property<T>>
{
    /**
     * Обновление состояния заданного поля.
     *
     * @param context контекст, в котором обновляется поле. Содержит данные о всей форме, в которой происходит
     * обновление
     * @param property обновляемое поле
     * @param callback обработчик результата применения обновления
     */
    void refreshProperty(PropertyContainerContext context, P property, AsyncCallback<Boolean> callback);
}
