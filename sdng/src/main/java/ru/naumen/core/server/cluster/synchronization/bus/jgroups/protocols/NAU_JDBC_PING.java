package ru.naumen.core.server.cluster.synchronization.bus.jgroups.protocols;

import static java.sql.ResultSet.CONCUR_UPDATABLE;
import static java.sql.ResultSet.TYPE_FORWARD_ONLY;
import static ru.naumen.core.server.cluster.synchronization.bus.jgroups.protocols.JGroupsProtocolUtils.addressFromString;
import static ru.naumen.core.server.cluster.synchronization.bus.jgroups.protocols.JGroupsProtocolUtils.addressToString;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.jgroups.Address;
import org.jgroups.Event;
import org.jgroups.PhysicalAddress;
import org.jgroups.annotations.Property;
import org.jgroups.protocols.JDBC_PING2;
import org.jgroups.protocols.PingData;
import org.jgroups.stack.IpAddress;
import org.jgroups.util.NameCache;
import org.jgroups.util.Responses;
import org.jgroups.util.Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Собственный протокол обнаружения, который заточен под наши нужды
 * - ежеминутный пинг каждой ноды
 * - атомарное обновление информации в бд
 * - без синхронизаций за счет upsert операций под каждую бд
 *
 * <AUTHOR>
 * @since 20.06.2023
 */
public class NAU_JDBC_PING extends JDBC_PING2 //NOSONAR сохраняем нейминг в традиции JGroups
{
    private static final Logger LOG = LoggerFactory.getLogger(NAU_JDBC_PING.class);

    @Property(description = "Вставка нового значения, если отсутствует, иначе обновление текущего")
    protected String upsert_single_sql; //NOSONAR сохраняем нейминг в традиции JGroups

    @Property(description = "Schema базы данных")
    protected String schema;

    @Override
    public void findMembers(final List<Address> members, final boolean initial_discovery, Responses responses)
    {
        super.findMembers(members, initial_discovery, responses);
        // пишем инфу о себе при каждой валидации состава кластера, чтобы не допустить сплит-брейнов
        writeOwnInformation();
    }

    private void writeOwnInformation()
    {
        PhysicalAddress physicalAddress = (PhysicalAddress)down(new Event(Event.GET_PHYSICAL_ADDRESS, local_addr));
        PingData data = new PingData(local_addr, is_server, NameCache.get(local_addr), physicalAddress).coord(is_coord);
        writeToDB(data, cluster_name);
    }

    @Override
    protected void writeToDB(PingData data, String clustername) //NOSONAR переходим на несинхронный режим
    {
        final Connection connection = getDBConnection();
        if (connection != null)
        {
            try
            {
                upsert(connection, data, clustername);
            }
            catch (SQLException e)
            {
                LOG.error(Util.getMessage("FailedToStorePingDataInDatabase"), e);
            }
            finally
            {
                closeConnection(connection);
            }
        }
        else
        {
            LOG.error(Util.getMessage("FailedToStorePingDataInDatabase")); //NOSONAR ложная сработка
        }
    }

    private void upsert(Connection connection, PingData data, String clustername)
            throws SQLException
    {
        try (PreparedStatement ps = connection.prepareStatement(upsert_single_sql))
        {
            Address address = data.getAddress();
            String addr = addressToString(address);
            String name = NameCache.get(address);
            IpAddress ipAddr = (IpAddress)data.getPhysicalAddr();
            String ip = ipAddr.toString();
            ps.setString(1, addr);
            ps.setString(2, name);
            ps.setString(3, clustername);
            ps.setString(4, ip);
            ps.setBoolean(5, data.isCoord());
            ps.executeUpdate();
        }
    }

    @Override
    protected void delete(Connection connection, String clustername, //NOSONAR переходим на несинхронный режим
            Address addressToDelete)
            throws SQLException
    {
        try (PreparedStatement ps = connection.prepareStatement(this.delete_single_sql))
        {
            ps.setString(1, addressToString(addressToDelete));
            ps.executeUpdate();
        }
    }

    @Override
    protected List<PingData> readFromDB(String cluster) throws Exception
    {
        try (Connection conn = getConnection();
             PreparedStatement ps = prepare(conn, select_all_pingdata_sql, TYPE_FORWARD_ONLY, CONCUR_UPDATABLE))
        {
            ps.setString(1, cluster);
            try (ResultSet resultSet = ps.executeQuery())
            {
                reads++;
                List<PingData> retval = new ArrayList<>();
                while (resultSet.next())
                {
                    String uuid = resultSet.getString(1);
                    String name = resultSet.getString(2);
                    String ip = resultSet.getString(3);
                    boolean coord = resultSet.getBoolean(4);
                    Address addr = addressFromString(uuid);
                    IpAddress ipAddr = new IpAddress(ip);
                    PingData data = new PingData(addr, true, name, ipAddr).coord(coord);
                    retval.add(data);
                }
                return retval;
            }
        }
    }

    @Override
    protected Connection getConnection() throws SQLException
    {
        Connection connection = super.getConnection();
        connection.setSchema(schema);
        return connection;
    }

    private Connection getDBConnection()
    {
        try
        {
            return getConnection();
        }
        catch (SQLException e)
        {
            LOG.error(Util.getMessage("CouldNotOpenConnectionToDatabase"), e);
            return null;
        }
    }

    private static void closeConnection(final Connection connection)
    {
        try
        {
            connection.close();
        }
        catch (SQLException e)
        {
            LOG.error(Util.getMessage("ErrorClosingConnectionToJDBCPINGDatabase"), e);
        }
    }
}
