package ru.naumen.core.server.idstorage;

import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.JavaType;

import ru.naumen.core.server.hibernate.IdJavaType;
import ru.naumen.core.server.hquery.criterion.InCriterionWithStorage;
import ru.naumen.core.shared.IUUIDIdentifiable;

/**
 * Сущность для хранения id-шников БО для {@link InCriterionWithStorage}
 *
 * <AUTHOR>
 * @since 22 янв. 2019 г.
 */
@Entity
@Table(name = IdsResult.TABLE_NAME)
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.TRANSACTIONAL)
public class IdsResult implements IUUIDIdentifiable
{
    private static final long serialVersionUID = 5953079469169182922L;

    public static final String TABLE_NAME = "tbl_sys_id_storage";

    @Id
    @Column(name = "id")
    @JavaType(IdJavaType.class)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SEQ_GEN")
    @SequenceGenerator(name = "SEQ_GEN", sequenceName = "objectid_sequence", allocationSize = 100)
    private Long id;

    @Column(name = "session_uuid")
    private String sessionUUID;

    @Column(name = "bo_id")
    private Long boId;

    public IdsResult()
    {
    }

    public IdsResult(Long boId, String sessionUUID)
    {
        this.boId = boId;
        this.sessionUUID = sessionUUID;
    }

    public Long getBoUUID()
    {
        return boId;
    }

    public long getId()
    {
        return id;
    }

    @Override
    public String getUUID()
    {
        return sessionUUID;
    }

    public void setBoUUID(Long boUUID)
    {
        this.boId = boUUID;
    }

    public void setId(long id)
    {
        this.id = id;
    }

    public void setUUID(String sessionUUID)
    {
        this.sessionUUID = sessionUUID;
    }
}
