package ru.naumen.core.server.common;

import java.util.Map;
import java.util.Objects;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.collect.Maps;

import ru.naumen.commons.server.utils.ReflectionTools;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.SpringContext;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;
import ru.naumen.core.server.autorize.GrantedPermission;
import ru.naumen.core.server.bo.LazyBO;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.Accessors;
import ru.naumen.metainfo.shared.Constants.CustomAccessors;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Вспомогательные методы для получения значения атрибута объекта через {@link Accessor}
 *
 * <AUTHOR>
 */
@Component
@SuppressWarnings("unchecked")
public class AccessorHelper
{
    private static final Logger LOG = LoggerFactory.getLogger(AccessorHelper.class);

    private final MetainfoService metainfoService;
    private final AuthorizationRunnerService authRunner;
    private final SpringContext springContext;
    private final Map<String, Accessor<?>> accessorCache = Maps.newConcurrentMap();
    private final Map<String, Accessor<?>> customAccessorCache = Maps.newConcurrentMap();

    @Inject
    public AccessorHelper(
            MetainfoService metainfoService,
            AuthorizationRunnerService authRunner,
            SpringContext springContext)
    {
        this.metainfoService = metainfoService;
        this.authRunner = authRunner;
        this.springContext = springContext;
    }

    /**
     * Возвращает {@link Accessor} для атрибута
     *
     * @param attr метаинформация атрибута
     * @return {@link Accessor} для атрибута
     */
    @SuppressWarnings({ "rawtypes" })
    public <T> Accessor<T> getAccessor(Attribute attr)
    {
        Objects.requireNonNull(attr, "Attribute can't be null");
        final String accessorCode = attr.getAccessor();
        if (StringUtilities.isEmpty(accessorCode))
        {
            LOG.warn("{} has no accessor!", attr);
            return getOrPutFromCache(accessorCache, attr.isHardcoded() ? Accessors.REFLECTION : Accessors.FLEX);
        }
        return getOrPutFromCache(accessorCache, accessorCode);
    }

    /**
     * Возвращает {@link Accessor} для атрибута используемого в скрипте или при создании плановых версий
     *
     * @param attr метаинформация атрибута
     * @return {@link Accessor} для атрибута
     */
    @SuppressWarnings({ "rawtypes" })
    public <T> Accessor<T> getCustomAccessor(Attribute attr)
    {
        Objects.requireNonNull(attr, "Attribute can't be null");
        String accessorCode = attr.getAccessor();
        final String customAccessorCode = CustomAccessors.CUSTOM + accessorCode;
        return CustomAccessors.ALL.contains(customAccessorCode)
                ? getOrPutFromCache(customAccessorCache, customAccessorCode)
                : getOrPutFromCache(accessorCache, accessorCode);
    }

    /**
     * Возвращает значение атрибута объекта
     *
     * @param <T> тип значения атрибута
     * @param obj объект значение атрибута которого требуется получить
     * @param attribute атрибут значение которого требуется получить
     * @return значение атрибута
     */
    public <T> T getAttributeValue(Object obj, Attribute attribute)
    {
        return getAttributeValue(obj, attribute, null);
    }

    /**
     * Возвращает значение атрибута объекта
     *
     * @param <T> тип значения атрибута
     * @param obj объект значение атрибута которого требуется получить
     * @param attribute атрибут значение которого требуется получить
     * @param limit максимальное количество объектов
     * @return значение атрибута
     */
    public <T> T getAttributeValue(Object obj, Attribute attribute, @Nullable Integer limit)
    {
        final Object unwrappedObj = obj instanceof LazyBO && !Constants.LazyBO.FIELDS.contains(attribute.getCode())
                ? ((LazyBO<?>)obj).getObject()
                : obj;

        final Accessor<Object> accessor = getAccessor(attribute);
        return limit == null
                ? (T)accessor.get(unwrappedObj, attribute)
                : (T)accessor.get(unwrappedObj, attribute, limit);
    }

    /**
     * Возвращает значение по умолчанию вычислимого атрибута объекта
     *
     * @param <T> тип значения атрибута
     * @param obj объект значение атрибута которого требуется получить
     * @param attribute атрибут значение которого требуется получить
     * @return значение атрибута
     */
    private <T> T getAttributeValueWithoutCalculation(Object obj, Attribute attribute)
    {
        final Accessor<Object> accessor = getAccessor(attribute);
        if (!(accessor instanceof ComputableAccessor))
        {
            return getAttributeValue(obj, attribute, null);
        }

        return (T)((ComputableAccessor)accessor).getDefaultValueWithResolver(attribute);
    }

    /**
     * Возвращает значение атрибута объекта
     *
     * @param <T> тип значения атрибута
     * @param obj объект значение атрибута которого требуется получить
     * @param attrCode код атрибута значение которого требуется получить
     * @return значение атрибута
     */
    public <T> T getAttributeValue(Object obj, String attrCode)
    {
        return getAttributeValue(obj, attrCode, null);
    }

    /**
     * Возвращает значение атрибута объекта
     *
     * @param <T> тип значения атрибута
     * @param obj объект значение атрибута которого требуется получить
     * @param attrCode код атрибута значение которого требуется получить
     * @param limit максимальное количество объектов
     * @return значение атрибута
     */
    public <T> T getAttributeValue(Object obj, String attrCode, @Nullable Integer limit)
    {
        if (obj instanceof LazyBO && !Constants.LazyBO.FIELDS.contains(attrCode))
        {
            obj = ((LazyBO<?>)obj).getObject();
        }

        final MetaClass metaClass = metainfoService.getMetaClass(obj);
        final Attribute attribute = metaClass.getAttribute(attrCode);
        return (T)getAttributeValue(obj, attribute, limit);
    }

    /**
     * Возвращает значение вычислимого атрибута объекта, без проверки прав на его чтение
     *
     * @param <T> тип значения атрибута
     * @param obj объект значение атрибута которого требуется получить
     * @param attr атрибут, значение которого нужно получить
     * @return значение атрибута
     */
    public <T> T getComputableAttributeValueWithoutCalculation(final Object obj, final Attribute attr)
    {
        ClassFqn fqn = metainfoService.getClassFqn(obj);
        return authRunner.callWithGrantedPermission(new GrantedPermission(fqn).attrPermit(false, attr.getCode()),
                () -> getAttributeValueWithoutCalculation(obj, attr));
    }

    /**
     * Возвращает значение атрибута объекта, без проверки прав на его чтение
     *
     * @param <T> тип значения атрибута
     * @param obj объект значение атрибута которого требуется получить
     * @param attr атрибут, значение которого нужно получить
     * @return значение атрибута
     */
    public <T> T getAttributeValueWithoutPermission(final Object obj, final Attribute attr)
    {
        ClassFqn fqn = metainfoService.getClassFqn(obj);
        return authRunner.callWithGrantedPermission(new GrantedPermission(fqn).attrPermit(false, attr.getCode()),
                () -> getAttributeValue(obj, attr));
    }

    /**
     * Возвращает значение атрибута объекта, без проверки прав на его чтение
     *
     * @param <T> тип значения атрибута
     * @param obj объект значение атрибута которого требуется получить
     * @param attr атрибут, значение которого нужно получить
     * @return значение атрибута
     */
    public <T> T getAttributeValueWithoutPermission(final Object obj, final Attribute attr, final Integer limit)
    {
        ClassFqn fqn = metainfoService.getClassFqn(obj);
        return authRunner.callWithGrantedPermission(new GrantedPermission(fqn).attrPermit(false, attr.getCode()),
                () -> getAttributeValue(obj, attr, limit));
    }

    /**
     * Возвращает значение атрибута объекта, без проверки прав на его чтение
     *
     * @param <T> тип значения атрибута
     * @param obj объект значение атрибута которого требуется получить
     * @param attrCode код атрибута значение которого требуется получиь
     * @return значение атрибута
     */
    public <T> T getAttributeValueWithoutPermission(final Object obj, final String attrCode)
    {
        ClassFqn fqn = metainfoService.getClassFqn(obj);
        return authRunner.callWithGrantedPermission(new GrantedPermission(fqn).attrPermit(false, attrCode),
                () -> getAttributeValue(obj, attrCode));
    }

    /**
     * Возвращает значение атрибута объекта, без проверки прав на его чтение
     *
     * @param <T> тип значения атрибута
     * @param obj объект значение атрибута которого требуется получить
     * @param attrCode код атрибута значение которого требуется получиь
     * @param limit максимальное количество объетов
     * @return значение атрибута
     */
    public <T> T getAttributeValueWithoutPermission(final Object obj, final String attrCode, final Integer limit)
    {
        ClassFqn fqn = metainfoService.getClassFqn(obj);
        return authRunner.callWithGrantedPermission(new GrantedPermission(fqn).attrPermit(false, attrCode),
                () -> getAttributeValue(obj, attrCode, limit));
    }

    /**
     * Устанавливает значение атрибута
     *
     * @param obj      объект, значение атрибута которого необходимо изменить
     * @param attrCode код изменяемого атрибута
     * @param value    устанавливаемое значение
     */
    public void setAttributeValue(Object obj, String attrCode, @Nullable Object value)
    {
        if (obj instanceof LazyBO)
        {
            if (Constants.LazyBO.FIELDS.contains(attrCode))
            {
                ReflectionTools.setProperty(obj, attrCode, value);
            }
            obj = ((LazyBO<?>)obj).getObject();
        }
        MetaClass metaClass = metainfoService.getMetaClass(obj);
        Attribute attribute = metaClass.getAttribute(attrCode);
        getAccessor(attribute).set(obj, attribute, value);
    }

    /**
     * Устанавливает значение атрибута, без проверки прав на его запись
     *
     * @param obj      объект, значение атрибута которого необходимо изменить
     * @param attrCode код изменяемого атрибута
     * @param value    устанавливаемое значение
     */
    public void setAttributeValueWithoutPermission(final Object obj, final String attrCode,
            @Nullable final Object value)
    {
        ClassFqn fqn = metainfoService.getClassFqn(obj);
        authRunner.runWithGrantedPermission(new GrantedPermission(fqn).attrPermit(true, attrCode),
                () -> setAttributeValue(obj, attrCode, value));
    }

    /**
     * Метод возвращает закешированный {@link Accessor},
     * а если значение отсутствует в кеше, то возвращает {@link Accessor} из {@link SpringContext}
     * @param cache кеш, из которого создаем {@link Accessor}
     * @param accessorCode код {@link Accessor}, который ищем
     * @return {@link Accessor}
     * @param <T> Класс {@link Accessor}
     */
    private <T> Accessor<T> getOrPutFromCache(Map<String, Accessor<?>> cache, String accessorCode)
    {
        Accessor<T> result = (Accessor<T>)cache.get(accessorCode);
        if (result != null)
        {
            return result;
        }
        Accessor<T> value = springContext.getBean(accessorCode, Accessor.class);
        cache.put(accessorCode, value);
        return value;
    }
}
