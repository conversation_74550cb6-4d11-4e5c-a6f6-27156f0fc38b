package ru.naumen.core.server.maintenance.services;

import java.util.List;

import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.maintenance.dao.MaintenanceDao;
import ru.naumen.core.server.maintenance.domain.Maintenance;

@Component
@DependsOn({ "sessionFactory" })
@Transactional
public class MaintenanceStorageServiceImpl implements MaintenanceStorageService
{
    private static final Logger LOG = LoggerFactory.getLogger(MaintenanceStorageServiceImpl.class);

    private final MaintenanceDao dao;

    @Inject
    public MaintenanceStorageServiceImpl(final MaintenanceDao dao)
    {
        LOG.info("Initializing maintenance storage");
        this.dao = dao;
        final List<Maintenance> allSortedByDateDesc = dao.getAllSortedByDateDesc();
        final int foundMaintenanceObjects = allSortedByDateDesc.size();
        if (allSortedByDateDesc.isEmpty())
        {
            LOG.info("Maintenance not found, saving the default one");
            dao.save(new Maintenance());
        }
        else if (foundMaintenanceObjects > 1)
        {
            LOG.warn("Found more than one maintenance object. {}/{} will be deleted", foundMaintenanceObjects - 1,
                    foundMaintenanceObjects);
            for (int i = 1; i < foundMaintenanceObjects; i++)
            {
                final Maintenance maintenance = allSortedByDateDesc.get(i);
                LOG.info("Deleting {}", maintenance);
                dao.delete(maintenance);
            }
        }
    }

    @Nonnull
    @Override
    public Maintenance getMaintenance(MaintenanceAccessMode lockMode)
    {
        LOG.trace("Getting maintenance");
        return dao.getMostRecentMaintenance(lockMode);
    }

    @Override
    public void persistMaintenance(final Maintenance maintenance)
    {
        dao.save(maintenance);
    }

    @Override
    public boolean isActualMaintenance(String maintenanceUUID)
    {
        Maintenance maintenance = dao.getMostRecentMaintenance(MaintenanceAccessMode.READ);
        return maintenanceUUID.equals(maintenance.getUUID());
    }
}
