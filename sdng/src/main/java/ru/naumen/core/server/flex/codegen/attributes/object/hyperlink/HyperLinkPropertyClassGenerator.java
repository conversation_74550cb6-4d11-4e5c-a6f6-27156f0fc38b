package ru.naumen.core.server.flex.codegen.attributes.object.hyperlink;

import static ru.naumen.core.server.flex.codegen.JavassistHelper.ctClassFromJavaClass;
import static ru.naumen.core.server.flex.codegen.TypeAnnotationGeneratorHelper.EMPTY_GENERATOR;
import static ru.naumen.core.server.flex.codegen.attributes.AttributeGenConstants.FQN_PREFIX;
import static ru.naumen.core.server.flex.codegen.attributes.object.CommonPropertyClassGenerator.generatePropertyFqnPropertyMethods;
import static ru.naumen.core.server.flex.codegen.attributes.object.CommonPropertyClassGenerator.generatePropertyPropertyMethods;
import static ru.naumen.core.shared.Constants.MAX_HYPERLINK_TITLE_LENGTH;
import static ru.naumen.core.shared.Constants.MAX_HYPERLINK_URL_LENGTH;

import javassist.CannotCompileException;
import javassist.CtClass;
import javassist.NotFoundException;
import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.core.server.flex.codegen.GenContext;
import ru.naumen.core.server.flex.codegen.JavassistHelper.EmbeddedClassGenerationResult;
import ru.naumen.core.server.flex.codegen.attributes.object.CommonPropertyClassGenerator;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Генерирует класс для значения flex-атрибута типа "Гиперссылка".
 *
 * <AUTHOR>
 * @since 18.12.2023
 */
final class HyperLinkPropertyClassGenerator
{
    private static final String URL_FIELD_NAME = "url";
    private static final String TEXT_FIELD_NAME = "text";
    private static final String URL_CAPITALIZED_FIELD_NAME = "Url";
    private static final String TEXT_CAPITALIZED_FIELD_NAME = "Text";

    private static final String PROPERTY_BASE_TYPE_NAME = Hyperlink.class.getTypeName();

    public static final String TITLE_COLUMN_SUFFIX = "_t";
    public static final String URL_COLUMN_SUFFIX = "_u";

    private HyperLinkPropertyClassGenerator()
    {
    }

    /**
     * Генерирует класс для значения property flex-атрибута типа "Гиперссылка".
     *
     * @param columnNamePrefix префикс наименования колонки для property flex-атрибута;
     * @param attribute        атрибут, для которого будет генерироваться класс;
     * @return сгенерированный класс;
     */
    public static CtClass generatePropertyClass(String columnNamePrefix, Attribute attribute,
            GenContext context) throws NotFoundException, CannotCompileException
    {
        final CtClass propertyBaseClass = context.pool().get(PROPERTY_BASE_TYPE_NAME);
        final EmbeddedClassGenerationResult generationResult = CommonPropertyClassGenerator.generatePropertyClass(
                propertyBaseClass, attribute, context);

        final CtClass propertyClass = generationResult.getGeneratedClass();
        if (generationResult.isNew())
        {
            generatePropertyClassPropertyMethods(propertyClass, columnNamePrefix, context);
        }

        return propertyClass;
    }

    /**
     * Генерирует класс для значения property fqn flex-атрибута типа "Гиперссылка".
     *
     * @param columnNamePrefix префикс наименования колонки для property fqn flex-атрибута;
     * @param attribute        атрибут, для которого будет генерироваться класс;
     * @return сгенерированный класс;
     */
    public static CtClass generatePropertyFqnClass(String columnNamePrefix,
            Attribute attribute,
            GenContext context) throws NotFoundException, CannotCompileException
    {
        final CtClass propertyBaseClass = context.pool().get(PROPERTY_BASE_TYPE_NAME);
        final EmbeddedClassGenerationResult generationResult = CommonPropertyClassGenerator.generatePropertyClass(
                propertyBaseClass, FQN_PREFIX, attribute, context);

        final CtClass propertyFqnClass = generationResult.getGeneratedClass();
        if (generationResult.isNew())
        {
            generatePropertyFqnClassPropertyMethods(propertyFqnClass, columnNamePrefix, context);
        }

        return propertyFqnClass;
    }

    private static void generatePropertyClassPropertyMethods(CtClass propertyClass, String columnNamePrefix,
            GenContext context) throws CannotCompileException, NotFoundException
    {
        final CtClass stringClass = ctClassFromJavaClass(context.pool(), String.class);
        generatePropertyPropertyMethods(propertyClass, TEXT_FIELD_NAME,
                columnNamePrefix + TITLE_COLUMN_SUFFIX, MAX_HYPERLINK_TITLE_LENGTH, TEXT_CAPITALIZED_FIELD_NAME,
                stringClass, EMPTY_GENERATOR);
        generatePropertyPropertyMethods(propertyClass, URL_FIELD_NAME,
                columnNamePrefix + URL_COLUMN_SUFFIX, MAX_HYPERLINK_URL_LENGTH, URL_CAPITALIZED_FIELD_NAME, stringClass,
                EMPTY_GENERATOR);
    }

    private static void generatePropertyFqnClassPropertyMethods(CtClass propertyFqnClass, String columnNamePrefix,
            GenContext context) throws NotFoundException, CannotCompileException
    {
        final CtClass stringClass = ctClassFromJavaClass(context.pool(), String.class);
        generatePropertyFqnPropertyMethods(propertyFqnClass, TEXT_FIELD_NAME,
                columnNamePrefix + TITLE_COLUMN_SUFFIX, MAX_HYPERLINK_TITLE_LENGTH, TEXT_CAPITALIZED_FIELD_NAME,
                stringClass, EMPTY_GENERATOR);
        generatePropertyFqnPropertyMethods(propertyFqnClass, URL_FIELD_NAME,
                columnNamePrefix + URL_COLUMN_SUFFIX, MAX_HYPERLINK_URL_LENGTH, URL_CAPITALIZED_FIELD_NAME, stringClass,
                EMPTY_GENERATOR);
    }
}
