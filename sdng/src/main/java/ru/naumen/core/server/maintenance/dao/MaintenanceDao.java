package ru.naumen.core.server.maintenance.dao;

import java.util.List;

import ru.naumen.core.server.bo.IDao;
import ru.naumen.core.server.maintenance.domain.Maintenance;
import ru.naumen.core.server.maintenance.services.MaintenanceAccessMode;

/**
 * {@link IDao DAO} для {@link Maintenance режима обслуживания}
 */
public interface MaintenanceDao
{
    void delete(Maintenance maintenance);

    List<Maintenance> getAllSortedByDateDesc();

    void save(Maintenance maintenance);

    Maintenance getMostRecentMaintenance(MaintenanceAccessMode lockMode);
}
