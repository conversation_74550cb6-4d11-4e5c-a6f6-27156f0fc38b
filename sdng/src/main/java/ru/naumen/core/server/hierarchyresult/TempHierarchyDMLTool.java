package ru.naumen.core.server.hierarchyresult;

import static ru.naumen.commons.shared.utils.StringUtilities.EMPTY;
import static ru.naumen.commons.shared.utils.StringUtilities.join;
import static ru.naumen.core.server.hierarchyresult.TempTableConstants.*;
import static ru.naumen.core.server.hierarchyresult.TemporaryHierarchyResult.TABLE_NAME;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.AppContext;
import ru.naumen.core.server.hibernate.DataBaseInfo;

/**
 * Вспомогательные методы для манипуляции с запросами к временным таблицам,
 * использующиеся в иерархических списках
 *
 * <AUTHOR>
 * @since 11.08.23
 */
@Component
public class TempHierarchyDMLTool
{
    private static final String INSERT_INTO = "insert into %s(%s, %s) %s";
    private static final int BATCH_INSERT_SIZE = 1000;

    private final DataBaseInfo dataBaseInfo;

    @Inject
    public TempHierarchyDMLTool(DataBaseInfo dataBaseInfo)
    {
        this.dataBaseInfo = dataBaseInfo;
    }

    public static String formatInsertHql(String hql)
    {
        return String.format(INSERT_INTO, TemporaryHierarchyResult.class.getSimpleName(),
                OBJECT_ID_HIBERNATE, HIERARCHY_ID_HIBERNATE, hql);
    }

    public List<String> generateSaveHqlForPlainGroup(String groupUuid, List<Long> ids)
    {
        List<Long> idsList = new ArrayList<>(ids);
        if (!dataBaseInfo.isMssql() || ids.size() <= BATCH_INSERT_SIZE)
        {
            return List.of(createInsertSql(ids, groupUuid));
        }
        int count = ids.size() % BATCH_INSERT_SIZE == 0
                ? ids.size() / BATCH_INSERT_SIZE
                : ids.size() / BATCH_INSERT_SIZE + 1;
        List<String> insertsQuery = new ArrayList<>(count);
        for (int i = 0; i < count; i++)
        {
            int currentStartIdx = i * BATCH_INSERT_SIZE;
            int currentEndIdx = currentStartIdx + BATCH_INSERT_SIZE;
            List<Long> sublist = idsList.subList(currentStartIdx, Math.min(currentEndIdx, ids.size()));
            String query = createInsertSql(sublist, groupUuid);
            insertsQuery.add(query);
        }
        return insertsQuery;
    }

    public String createInsertSql(List<Long> columns, String groupUuid)
    {
        String queryHeader = generateInsertSqlWithoutValues(Arrays.asList(OBJECT_ID, GROUP_ID));
        String queryValues = generateSqlForIntoValues(columns, groupUuid);
        return queryHeader + queryValues;
    }

    private String generateInsertSqlWithoutValues(List<String> columnsName)
    {
        StringBuilder sqlQuery = new StringBuilder();
        String tableNamePrefix = dataBaseInfo.isMssql() ? "#" : EMPTY;
        sqlQuery.append("insert into ")
                .append(tableNamePrefix)
                .append(TABLE_NAME)
                .append(" (")
                .append(join(columnsName, ","))
                .append(')');
        return sqlQuery.toString();
    }

    private String generateSqlForIntoValues(List<Long> columns, String groupUuid)
    {
        String rowDelimiter = dataBaseInfo.isOracle() ? " union all" : ",";
        String rowStart = dataBaseInfo.isOracle() ? " select " : " (";
        String rowEnd = dataBaseInfo.isOracle() ? " from dual" : ")";
        StringBuilder sqlQuery = new StringBuilder();
        if (!dataBaseInfo.isOracle() && !AppContext.isReadOnly())
        {
            sqlQuery.append(" values");
        }

        boolean isFirstRow = true;
        for (Object id : columns)
        {
            if (!isFirstRow)
            {
                sqlQuery.append(rowDelimiter);
            }
            isFirstRow = false;
            sqlQuery.append(rowStart).append(id).append(",'").append(groupUuid).append('\'').append(rowEnd);
        }
        return sqlQuery.toString();
    }

    public String getTabelNamePrefix()
    {
        return dataBaseInfo.isMssql() ? "#" : EMPTY;
    }
}