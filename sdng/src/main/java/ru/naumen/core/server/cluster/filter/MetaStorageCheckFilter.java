package ru.naumen.core.server.cluster.filter;

import static org.springframework.http.HttpStatus.SERVICE_UNAVAILABLE;
import static ru.naumen.core.server.cluster.external.throttling.WorkloadSource.REQUEST;

import java.io.IOException;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.cluster.external.ClusterInfoService;
import ru.naumen.core.server.cluster.external.throttling.MetaCheckThrottler;
import ru.naumen.core.server.cluster.license.SecurityClusterService;
import ru.naumen.core.server.cluster.synchronization.reload.MetaStorageChangesService;

/**
 * Фильтр, проверяющий версию метаинформации.
 * Работает только в кластере, проверяет ресты и оператора
 * <AUTHOR>
 * @since 18.12.18
 */
public class MetaStorageCheckFilter implements Filter
{
    private static final String INSTALL_LICENSE = "install-license";
    private static final String READINESS_PROBE_ENDPOINT = "health/readiness";
    private static final String LIVENESS_PROBE_ENDPOINT = "health/liveness";

    private SecurityClusterService securityClusterService;
    MetaCheckThrottler nodeChecker;
    boolean isCluster;

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException
    {
        if (isCluster && nodeChecker.notValidNodeState(REQUEST))
        {
            // если это загрузка лицензии - то такой запрос пропускаем
            final String pathInfo = ((HttpServletRequest)request).getPathInfo();
            if (pathInfo != null && pathInfo.endsWith(INSTALL_LICENSE))
            {
                chain.doFilter(request, response);
                return;
            }
            // если лицензия не загружена и это readiness или liveness probe, то пропускаем запрос
            if (pathInfo != null && (pathInfo.endsWith(INSTALL_LICENSE) || isProbe(pathInfo)))
            {
                chain.doFilter(request, response);
                return;
            }
            final HttpServletResponse httpServletResponse = (HttpServletResponse)response;
            httpServletResponse.sendError(SERVICE_UNAVAILABLE.value(), MetaStorageChangesService.META_CHANGED_MSG);
            return;
        }
        chain.doFilter(request, response);
    }

    private boolean isProbe(String pathInfo)
    {
        return (pathInfo.endsWith(READINESS_PROBE_ENDPOINT) && !securityClusterService.isClusterEnabled())
               || pathInfo.endsWith(LIVENESS_PROBE_ENDPOINT);
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException
    {
        final SpringContext springContext = SpringContext.getInstance();
        isCluster = springContext.getBean(ClusterInfoService.class).isAnyClusterMode();
        if (isCluster)
        {
            nodeChecker = springContext.getBean(MetaCheckThrottler.class);
            securityClusterService = springContext.getBean(SecurityClusterService.class);
        }
    }

    @Override
    public void destroy()
    {
        //do nothing
    }
}
