package ru.naumen.core.server.flex.codegen.attributes.simple.specific;

import static ru.naumen.core.server.flex.codegen.TypeAnnotationGeneratorHelper.BIGINT_GENERATOR;

import jakarta.inject.Inject;
import ru.naumen.core.server.flex.attr.AttrStrategyComponent;
import ru.naumen.core.server.flex.codegen.attributes.simple.SimpleAttributeGenerator;
import ru.naumen.metainfo.shared.Constants;

/**
 * Генерирует методы и необходимые аннотации для атрибута типа Целое число.
 *
 * <AUTHOR>
 * @since 18.12.2023
 */
@AttrStrategyComponent(types = { Constants.IntegerAttributeType.CODE })
public class IntegerAttributeGenerator extends SimpleAttributeBaseGenerator
{
    @Inject
    public IntegerAttributeGenerator()
    {
        // в hbm билдере был указан тип long, а не int
        super(new SimpleAttributeGenerator(Long.class, BIGINT_GENERATOR, true));
    }
}
