package ru.naumen.core.server.flex.codegen.attributes.object.timer;

import static javassist.bytecode.AnnotationsAttribute.visibleTag;
import static ru.naumen.core.server.flex.codegen.JavassistHelper.addAttribute;
import static ru.naumen.core.server.flex.codegen.JavassistHelper.createColumnAnnotation;
import static ru.naumen.core.server.flex.codegen.JavassistHelper.getConstPool;

import javassist.CtClass;
import javassist.CtMethod;
import javassist.bytecode.AnnotationsAttribute;
import javassist.bytecode.ConstPool;
import javassist.bytecode.annotation.Annotation;
import ru.naumen.core.server.flex.codegen.GenContext;
import ru.naumen.core.server.flex.codegen.attributes.MethodAnnotationsGenerator;
import ru.naumen.metainfo.shared.Constants.BackTimerAttributeType;
import ru.naumen.metainfo.shared.Constants.TimerAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Генерирует проперти {@link ru.naumen.core.server.timer.BackTimer#elapsedFromOverdue} для
 * {@link jakarta.persistence.Embeddable} класса {@link ru.naumen.core.server.timer.BackTimer}.
 *
 * <AUTHOR>
 * @since 18.12.2023
 */
public class TimerEmbeddedEFOPropertiesAnnotationGenerator implements MethodAnnotationsGenerator
{
    private final boolean timerInitialized;

    public TimerEmbeddedEFOPropertiesAnnotationGenerator(boolean timerInitialized)
    {
        this.timerInitialized = timerInitialized;
    }

    @Override
    public void generate(Attribute attribute, CtMethod method, CtClass persistentClass, GenContext context)
    {
        final String timerCode = attribute.getType().getProperty(TimerAttributeType.DEFINITION);
        if (timerCode != null)
        {
            final ConstPool constPool = getConstPool(persistentClass);
            final String columnName = timerCode + BackTimerAttributeType.ELAPSED_FROM_OVERDUE_SUFFIX;
            final Annotation column = timerInitialized
                    ? createColumnAnnotation(constPool, columnName, true, false, false)
                    : createColumnAnnotation(constPool, columnName, true);
            final AnnotationsAttribute annotationsAttribute = new AnnotationsAttribute(constPool, visibleTag);
            annotationsAttribute.addAnnotation(column);
            addAttribute(method, annotationsAttribute);
        }
    }
}
