package ru.naumen.core.server.flex.codegen.attributes.object.link;

import static java.util.Collections.singletonList;
import static jakarta.persistence.FetchType.LAZY;
import static org.hibernate.annotations.OptimisticLockType.NONE;
import static ru.naumen.core.server.flex.FlexHelper.getColumnName;
import static ru.naumen.core.server.flex.FlexHelper.getIndexName;
import static ru.naumen.core.server.flex.codegen.JavassistHelper.*;
import static ru.naumen.core.server.flex.codegen.SourceGenHelper.*;
import static ru.naumen.core.server.flex.codegen.attributes.AttributeGenConstants.COLUMN_KEY;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.javapoet.ClassName;

import jakarta.inject.Inject;
import javassist.CannotCompileException;
import javassist.CtClass;
import javassist.CtMethod;
import javassist.bytecode.AnnotationsAttribute;
import javassist.bytecode.ConstPool;
import javassist.bytecode.annotation.Annotation;
import ru.naumen.core.server.flex.attr.AttrStrategyComponent;
import ru.naumen.core.server.flex.codegen.GenContext;
import ru.naumen.core.server.flex.codegen.attributes.AttributeAppender;
import ru.naumen.core.server.flex.codegen.attributes.AttributePropertiesBuilder;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;

/**
 * Генерирует необходимые классы, методы и аннотации для flex-атрибута типа "Ссылка на БО".
 *
 * <AUTHOR>
 * @since 18.12.2023
 */
@AttrStrategyComponent(types = { Constants.ObjectAttributeType.CODE, Constants.CatalogItemAttributeType.CODE })
public class ObjectAttributeGenerator implements AttributeAppender
{
    private static void generateFqnGetterAnnotations(
            CtMethod getter,
            String mappingName,
            ConstPool constPool,
            String columnName,
            String indexName,
            String targetEntityName)
    {
        final Annotation column = createJoinColumnAnnotation(constPool, columnName, false, false, true);
        final Annotation index = createHibernateIndexAnnot(constPool, indexName);
        final Annotation optimisticLocking = createOptimisticLockingAnnotation(constPool, NONE);
        final Annotation manyToOne = createManyToOneAnnot(constPool, targetEntityName, LAZY);
        final Annotation access = createPropertyAccessAnnotation(constPool);
        final Annotation mapping = createMappingNameAnnotation(constPool, mappingName);
        final AnnotationsAttribute annotationsAttribute = createAnnotationsAttribute(constPool, column, index,
                optimisticLocking, manyToOne, access, mapping);
        addAttribute(getter, annotationsAttribute);
    }

    private static Collection<CtClass> generatePropertyFqnMethods(
            Attribute attribute,
            CtClass persistentClass,
            String columnName,
            ClassName relatedClassType,
            String indexName,
            String targetEntity) throws CannotCompileException
    {
        if (!persistentClass.isFrozen())
        {
            final CtMethod getter = attribute.getMetaClass().isSystemCase() && attribute.isHardcoded()
                    ? generateBasicAdditionalPropertyFqnGetter(persistentClass, attribute, relatedClassType)
                    : generateFlexAdditionalLinkPropertyFqnGetter(persistentClass, attribute, relatedClassType);
            final CtMethod setter = generateFlexPropertyFqnSetter(persistentClass, attribute, relatedClassType);

            final ConstPool constPool = getConstPool(persistentClass);
            generateFqnGetterAnnotations(getter, attribute.getPropertyFqn(), constPool, columnName, indexName,
                    targetEntity);

            persistentClass.addMethod(getter);
            persistentClass.addMethod(setter);
        }
        return List.of();
    }

    private static void generateGetterAnnotations(
            CtMethod getter,
            String mappingName,
            ConstPool constPool,
            String columnName,
            String indexName,
            String targetEntityName)
    {
        final Annotation joinColumn = createJoinColumnAnnotation(constPool, columnName);
        final Annotation index = createHibernateIndexAnnot(constPool, indexName);
        final Annotation manyToOne = createManyToOneAnnot(constPool, targetEntityName, LAZY);
        final Annotation access = createPropertyAccessAnnotation(constPool);
        final Annotation mapping = createMappingNameAnnotation(constPool, mappingName);
        final AnnotationsAttribute annotationsAttribute = createAnnotationsAttribute(constPool, index,
                joinColumn, manyToOne, access, mapping);
        addAttribute(getter, annotationsAttribute);
    }

    private static Collection<CtClass> generatePropertyMethods(
            Attribute attribute,
            CtClass persistentClass,
            String columnName,
            ClassName relatedClassType,
            String indexName,
            String targetEntity) throws CannotCompileException
    {
        if (!persistentClass.isFrozen())
        {
            final CtMethod getter = generateFlexOrBasicPropertyGetter(persistentClass, attribute, relatedClassType);
            final CtMethod setter = generateFlexOrBasicPropertySetter(persistentClass, attribute, relatedClassType);

            final ConstPool constPool = getConstPool(persistentClass);
            generateGetterAnnotations(getter, attribute.getCode(), constPool, columnName, indexName, targetEntity);

            persistentClass.addMethod(getter);
            persistentClass.addMethod(setter);
        }
        return List.of();
    }

    private final MetainfoService metainfoService;

    /**
     * Генерирует необходимые классы, методы и аннотации для flex-атрибута типа "Ссылка на БО".
     *
     * @param metainfoService сервис для работы с метаинформацией;
     */
    @Inject
    public ObjectAttributeGenerator(MetainfoService metainfoService)
    {
        this.metainfoService = metainfoService;
    }

    @Override
    public Collection<CtClass> appendProperty(Attribute attribute, CtClass persistentClass, GenContext context)
    {
        final String columnName = getColumnName(attribute);
        final ObjectAttributeType attributeType = attribute.getType().cast();
        final ClassFqn relatedMetaClass = attributeType.getRelatedMetaClass();
        final String rootJavaClassName = metainfoService.getJavaClassName(relatedMetaClass);
        final String targetEntity = relatedMetaClass.isClass()
                ? rootJavaClassName
                : metainfoService.getEntityJavaClassName(relatedMetaClass);
        final ClassName relatedType = ClassName.bestGuess(rootJavaClassName);
        final String indexName = getIndexName(attribute, null);

        return AttributePropertiesBuilder.build(attribute,
                c -> generatePropertyMethods(attribute, persistentClass, columnName, relatedType,
                        indexName, targetEntity),
                c -> generatePropertyFqnMethods(attribute, persistentClass, columnName, relatedType,
                        indexName, targetEntity),
                context);
    }

    @Override
    public String getAccessor()
    {
        return Constants.Accessors.FLEX;
    }

    @Override
    public List<String> getColumnNames(Attribute attribute)
    {
        return singletonList(getColumnName(attribute));
    }

    @Override
    public boolean isSupportUnique()
    {
        return true;
    }

    @Override
    public Map<Object, Object> getCommonProperties(Attribute attribute)
    {
        Map<Object, Object> map = HashMap.newHashMap(1);
        map.put(COLUMN_KEY, getColumnName(attribute));
        return map;
    }
}
