package ru.naumen.core.server.hibernate.dialect.function;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.query.ReturnableType;
import org.hibernate.query.sqm.function.AbstractSqmSelfRenderingFunctionDescriptor;
import org.hibernate.query.sqm.produce.function.StandardFunctionReturnTypeResolvers;
import org.hibernate.sql.ast.SqlAstTranslator;
import org.hibernate.sql.ast.spi.SqlAppender;
import org.hibernate.sql.ast.tree.SqlAstNode;
import org.hibernate.sql.ast.tree.expression.Literal;
import org.hibernate.type.StandardBasicTypes;
import org.hibernate.type.spi.TypeConfiguration;

import ru.naumen.core.server.script.api.criteria.IApiCriteriaColumn;

/**
 * Функция усечения даты, специфичная для MS SQL.<br>
 * Преобразует общие значения, описанные в
 * {@link ru.naumen.core.server.script.api.ISelectClauseApi#truncDate(IApiCriteriaColumn, String)},
 * специфичные для MS SQL.<br>
 * Позволяет использовать все допустимые значения параметра datepart функции DATEDIFF,
 * если указывать их явно в поле field.<br>
 * <a href='https://docs.microsoft.com/ru-ru/sql/t-sql/functions/datediff-transact-sql'>DATEDIFF</a>
 *
 * <AUTHOR>
 * @since 16.11.2021
 */
public class SQLServerTruncDateSQLFunction extends AbstractSqmSelfRenderingFunctionDescriptor
{
    public SQLServerTruncDateSQLFunction(String name, TypeConfiguration typeConfiguration)
    {
        super(name, null, StandardFunctionReturnTypeResolvers.invariant(
                typeConfiguration.getBasicTypeRegistry().resolve(StandardBasicTypes.TIMESTAMP)), null);
    }

    @Override
    public void render(
            SqlAppender sqlAppender,
            List<? extends SqlAstNode> sqlAstArguments,
            ReturnableType<?> returnType,
            SqlAstTranslator<?> walker)
    {
        final Literal fieldArg = (Literal)sqlAstArguments.get(1);
        final String field = StringUtils.strip((String)fieldArg.getLiteralValue(), "'");

        if (field.equals("day") || field.equals("dd") || field.equals("d"))
        {
            sqlAppender.appendSql("cast(");
            sqlAstArguments.getFirst().accept(walker);
            sqlAppender.appendSql(" As Date)");
        }
        else
        {
            sqlAppender.appendSql("DATEADD(");
            sqlAppender.appendSql(field);
            sqlAppender.appendSql(", DATEDIFF(");
            sqlAppender.appendSql(field);
            sqlAppender.appendSql(", 0, ");
            sqlAstArguments.getFirst().accept(walker);
            sqlAppender.appendSql("), 0)");
        }
    }
}
