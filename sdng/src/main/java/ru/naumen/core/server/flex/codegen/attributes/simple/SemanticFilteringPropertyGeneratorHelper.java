package ru.naumen.core.server.flex.codegen.attributes.simple;

import static javassist.bytecode.AnnotationsAttribute.visibleTag;
import static ru.naumen.core.server.flex.FlexHelper.getColumnName;
import static ru.naumen.core.server.flex.codegen.JavassistHelper.*;
import static ru.naumen.core.server.flex.codegen.SourceGenHelper.generateFlexPropertyGetter;
import static ru.naumen.core.server.flex.codegen.SourceGenHelper.generateFlexPropertySetter;

import java.util.Collection;
import java.util.List;

import org.springframework.javapoet.ClassName;
import org.springframework.javapoet.TypeName;

import javassist.CannotCompileException;
import javassist.CtClass;
import javassist.CtMethod;
import javassist.bytecode.AnnotationsAttribute;
import javassist.bytecode.ConstPool;
import javassist.bytecode.annotation.Annotation;
import javassist.bytecode.annotation.StringMemberValue;
import ru.naumen.core.server.flex.codegen.GenContext;
import ru.naumen.core.server.flex.codegen.attributes.MethodAnnotationsGenerator;
import ru.naumen.fts.server.dbsearch.TsVectorPgUserType;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Вспомогательный класс для генерации атрибутов, имеющих свойство "фильтрация с учетом морфологии"
 *
 * <AUTHOR>
 * @since 01 окт. 2019 г.
 */
public final class SemanticFilteringPropertyGeneratorHelper
{
    public static final String FTS_PREFIX = "Fts";
    public static final String FTS_COLUMN_PREFIX = "_fts";

    private static final TypeName STRING_TYPE = ClassName.get(String.class);

    private SemanticFilteringPropertyGeneratorHelper()
    {
    }

    private static final class PropertySemanticFilteringAnnotationsGenerator implements MethodAnnotationsGenerator
    {
        @Override
        public void generate(Attribute attribute, CtMethod method, CtClass persistentClass, GenContext context)
        {
            final ConstPool constPool = getConstPool(persistentClass);
            final Annotation columnAnnotation = createColumnAnnotation(constPool,
                    getColumnName(attribute) + FTS_COLUMN_PREFIX, true);
            columnAnnotation.addMemberValue("columnDefinition",
                    new StringMemberValue(TsVectorPgUserType.TS_VECTOR_TYPE_NAME, constPool));
            final Annotation typeAnnotation = createTypeAnnotation(constPool, TsVectorPgUserType.class);
            final Annotation accesssAnnotation = createPropertyAccessAnnotation(constPool);
            final Annotation mappinngAnnotation = createMappingNameAnnotation(constPool,
                    attribute.getPropertyFqn() + FTS_PREFIX);

            final AnnotationsAttribute annotationsAttribute = new AnnotationsAttribute(constPool, visibleTag);
            annotationsAttribute.addAnnotation(columnAnnotation);
            annotationsAttribute.addAnnotation(typeAnnotation);
            annotationsAttribute.addAnnotation(accesssAnnotation);
            annotationsAttribute.addAnnotation(mappinngAnnotation);

            addAttribute(method, annotationsAttribute);
        }
    }

    public static Collection<CtClass> generateSemanticFilteringPropertyMethods(Attribute attribute,
            CtClass persistentClass, GenContext context) throws CannotCompileException
    {
        if (!persistentClass.isFrozen())
        {
            final String propertyName = attribute.getPropertyFqn() + FTS_PREFIX;
            final CtMethod fqnGetter = generateFlexPropertyGetter(persistentClass, propertyName,
                    propertyName, STRING_TYPE);
            final CtMethod fqnSetter = generateFlexPropertySetter(persistentClass, propertyName,
                    propertyName, STRING_TYPE);

            final MethodAnnotationsGenerator generator = new PropertySemanticFilteringAnnotationsGenerator();
            generator.generate(attribute, fqnGetter, persistentClass, context);

            persistentClass.addMethod(fqnGetter);
            persistentClass.addMethod(fqnSetter);
        }
        return List.of();
    }
}
