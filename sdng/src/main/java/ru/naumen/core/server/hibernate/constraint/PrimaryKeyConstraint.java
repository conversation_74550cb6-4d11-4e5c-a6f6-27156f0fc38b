package ru.naumen.core.server.hibernate.constraint;

import ru.naumen.core.server.hibernate.DDLDialect;

/**
 * <AUTHOR>
 * @since 16.06.2011
 *
 */
public class PrimaryKeyConstraint implements Constraint
{
    private final String columnName;
    private String name;

    public PrimaryKeyConstraint(String columnName)
    {
        this.columnName = columnName;
    }

    public PrimaryKeyConstraint(String columnName, String name)
    {
        this(columnName);
        this.name = name;
    }

    public String getColumnName()
    {
        return columnName;
    }

    @Override
    public String getConstraintSQL(DDLDialect dialect)
    {
        return "primary key (" + columnName + ")"; //$NON-NLS-1$ //$NON-NLS-2$
    }

    @Override
    public String getName()
    {
        return this.name;
    }

    @Override
    public boolean isInline()
    {
        return false;
    }

    public void setName(String name)
    {
        this.name = name;
    }
}
