package ru.naumen.core.server.hierarchyresult;

import static ru.naumen.core.server.hierarchyresult.TempTableConstants.GROUP_ID;
import static ru.naumen.core.server.hierarchyresult.TempTableConstants.HIERARCHY_ID_HIBERNATE;
import static ru.naumen.core.server.hierarchyresult.TempTableConstants.OBJECT_ID;
import static ru.naumen.core.server.hierarchyresult.TempTableConstants.TEMP_TABLE_CACHE_REGION;
import static ru.naumen.core.server.hierarchyresult.TemporaryHierarchyResult.INDEX_NAME;
import static ru.naumen.core.server.hierarchyresult.TemporaryHierarchyResult.TABLE_NAME;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;

import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.Query;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.TransactionHelper;
import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.hibernate.DataBaseInfo;
import ru.naumen.core.server.hibernate.column.ColumnDescription;
import ru.naumen.core.server.hibernate.column.ColumnDescriptions;
import ru.naumen.core.server.hibernate.index.IndexDescription;
import ru.naumen.core.server.hibernate.table.TableDescription;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.hquery.HPredicate;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.shared.Constants.IDIdentifiableBase;
import ru.naumen.core.shared.utils.UUIDGenerator;

/**
 * Реализация сервиса для работы с временным хранилищем результатов запросов.
 *
 * <AUTHOR>
 * @since May 13, 2020
 */
@Component
public class TempStorageServiceImpl implements TempStorageService
{
    private static final Logger LOG = LoggerFactory.getLogger(TempStorageServiceImpl.class);

    private final TempHierarchyDMLTool tempHierarchyDMLTool;
    private final DataBaseInfo dataBaseInfo;
    private final SessionFactory sessionFactory;
    private final ThreadLocal<Map<String, String>> groupUuidMap = new ThreadLocal<>();
    private final ThreadLocal<String> transactionId = new ThreadLocal<>();

    @Inject
    public TempStorageServiceImpl(TempHierarchyDMLTool tempHierarchyDMLTool, DataBaseInfo dataBaseInfo,
            @Lazy SessionFactory sessionFactory)
    {
        this.tempHierarchyDMLTool = tempHierarchyDMLTool;
        this.dataBaseInfo = dataBaseInfo;
        this.sessionFactory = sessionFactory;
    }

    @EventListener
    public void onContextRefreshed(ContextRefreshedEvent event)
    {
        if (dataBaseInfo.isOracle())
        {
            TransactionRunner.run(() -> dropStorageTable(sessionFactory.getCurrentSession()));
            LOG.info("Temporary storage dropped.");
        }
    }

    @Override
    public void createStorageTable(Session session)
    {
        session.doWork(this::createTempTable);
    }

    @Override
    public void dropStorageTable(Session session)
    {
        session.doWork(this::dropTempTable);
    }

    @Override
    public String getGroupUuid(String key)
    {
        updateTransactionId();
        if (null == groupUuidMap.get())
        {
            groupUuidMap.set(new HashMap<>());
            TransactionHelper.beforeCommit(() ->
            {
                groupUuidMap.remove();
                transactionId.remove();
            });
        }
        return groupUuidMap.get().computeIfAbsent(key, k -> UUIDGenerator.get().nextUUID());
    }

    @Override
    public boolean isGroupExists(String key)
    {
        updateTransactionId();
        return null != groupUuidMap.get() && null != groupUuidMap.get().get(key);
    }

    @Override
    public void saveHierarchyGroup(String groupUuid, HCriteria criteria, Session session)
    {
        saveWithLogExecutionTime(() ->
        {
            HCriteria innerCriteria = HHelper.createOverCriteria(criteria).addCTESource(criteria);
            innerCriteria.setPredicate(HPredicate.DISTINCT);
            innerCriteria.addColumn(innerCriteria.getProperty(IDIdentifiableBase.ID));
            innerCriteria.addColumn("'" + groupUuid + "'", HIERARCHY_ID_HIBERNATE);
            String insertSql = TempHierarchyDMLTool.formatInsertHql(innerCriteria.createHql(session));
            Query query = session.createQuery(insertSql);
            query.setCacheRegion(TEMP_TABLE_CACHE_REGION);
            innerCriteria.getDelegate().setParameters(query);
            return query.executeUpdate();
        });
    }

    @Override
    public void savePlainGroup(String groupUuid, HCriteria criteria, Session session)
    {
        saveWithLogExecutionTime(() ->
        {
            criteria.addColumn("'" + groupUuid + "'", HIERARCHY_ID_HIBERNATE);
            String insertHql = TempHierarchyDMLTool.formatInsertHql(criteria.createHql(session));
            Query query = session.createQuery(insertHql);
            query.setCacheRegion(TEMP_TABLE_CACHE_REGION);
            criteria.getDelegate().setParameters(query);
            return query.executeUpdate();
        });
    }

    @Override
    public void savePlainGroup(String groupUuid, List<Long> ids, Session session)
    {
        saveWithLogExecutionTime(() ->
        {
            List<String> queries = tempHierarchyDMLTool.generateSaveHqlForPlainGroup(groupUuid, ids);
            int updatedRows = 0;
            for (String query : queries)
            {
                NativeQuery<?> sqlQuery = session.createNativeQuery(query);
                sqlQuery.addSynchronizedQuerySpace("");
                updatedRows += sqlQuery.executeUpdate();
            }
            return updatedRows;
        });
    }

    private static void saveWithLogExecutionTime(Callable<Integer> callable)
    {
        final long executionStartTime = System.currentTimeMillis();
        int updatedRows = 0;
        try
        {
            updatedRows += callable.call();
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
        finally
        {
            if (LOG.isDebugEnabled())
            {
                final long duration = System.currentTimeMillis() - executionStartTime;
                LOG.debug("Done({}) save hierarchy result, added rows = {}", duration, updatedRows);
            }
        }
    }

    private void createTempTable(Connection connection)
    {
        List<ColumnDescription> columns = Arrays.asList(ColumnDescriptions.bigint(OBJECT_ID),
                ColumnDescriptions.string(GROUP_ID));
        IndexDescription index = new IndexDescription(INDEX_NAME, TABLE_NAME, OBJECT_ID, GROUP_ID);
        createTempTable(connection, columns, List.of(index));
    }

    private void createTempTable(Connection connection, Collection<ColumnDescription> columns,
            Collection<IndexDescription> indexes)
    {
        try
        {
            DDLTool ddlTool = new DDLTool(connection);
            if (ddlTool.tempTableExists(TABLE_NAME))
            {
                return;
            }
            TableDescription table = new TableDescription(TABLE_NAME);
            table.setTemporary(true);
            columns.forEach(table::addColumn);
            ddlTool.createTable(table);
            String tableNamePrefix = tempHierarchyDMLTool.getTabelNamePrefix();
            indexes.forEach(idx ->
            {
                try
                {
                    ddlTool.createIndex(idx.getIndexName(), tableNamePrefix + TABLE_NAME, idx.getColumnNames());
                }
                catch (SQLException e)
                {
                    LOG.info("Skip create index {} on temp table", idx.getIndexName());
                }
            });
        }
        catch (SQLException e)
        {
            throw new FxException(e.getMessage(), e);
        }
    }

    private void dropTempTable(Connection connection)
    {
        try
        {
            DDLTool ddlTool = new DDLTool(connection);
            ddlTool.dropTable(TABLE_NAME);
        }
        catch (SQLException e)
        {
            throw new FxException(e.getMessage(), e);
        }
    }

    private void updateTransactionId()
    {
        String currentTransactionId = TransactionHelper.getTxId();
        if (null == currentTransactionId)
        {
            transactionId.remove();
            groupUuidMap.remove();
        }
        else if (!currentTransactionId.equals(transactionId.get()))
        {
            transactionId.set(currentTransactionId);
            groupUuidMap.remove();
        }
    }
}
