package ru.naumen.core.server.filestorage.spi.storages.operations.s3;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.amazonaws.services.s3.model.ObjectMetadata;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.filestorage.conf.Storage;
import ru.naumen.core.server.filestorage.conf.Storage.StorageType;
import ru.naumen.core.server.filestorage.spi.FileStorageSettingsService;
import ru.naumen.core.server.filestorage.spi.storages.operations.FileLocation;
import ru.naumen.core.server.util.MessageFacade;

/**
 * Операции по взаимодействию с S3-ФХ, которые позволяют также указать бакеты для выполнимых операций.
 * Данные методы вызываются из скриптов, т.к только там мы идем в обход заранее определенных бакетов для наших ФХ.
 * Подразумеваем, что работа происходит относительно активного ФХ S3-типа, только если в метод явно не передаем имя
 * исходного бакета
 *
 * <AUTHOR>
 * @since 01.06.2020
 */
@Component
@Lazy
public class S3StorageScriptOperations
{
    private final S3StorageOperations s3StorageOperations;
    private final FileStorageSettingsService settingsService;
    private final MessageFacade messages;

    @Inject
    public S3StorageScriptOperations(final S3StorageOperations s3StorageOperations,
            final FileStorageSettingsService settingsService,
            final MessageFacade messages)
    {
        this.s3StorageOperations = s3StorageOperations;
        this.settingsService = settingsService;
        this.messages = messages;
    }

    /**
     * Переместить файл из другого бакета
     *
     * @param filePath имя файла в другом бакете
     * @param sourceBucketName имя бакета, из которого будет перемещение
     * @param newFilePath новое имя файла
     */
    public void moveObject(String filePath, String sourceBucketName, String newFilePath)
    {
        moveObject(filePath, sourceBucketName, newFilePath, settingsService.getActiveStorage());
    }

    /**
     * Переместить файл из другого бакета
     *
     * @param filePath имя файла в другом бакете
     * @param sourceBucketName имя бакета, из которого будет перемещение
     * @param newFilePath новое имя файла
     * @param storage файловое хранилище
     */
    public void moveObject(String filePath, String sourceBucketName, String newFilePath, @Nullable Storage storage)
    {
        copyObject(filePath, sourceBucketName, newFilePath, storage);
        deleteObject(filePath, sourceBucketName, storage);
    }

    /**
     * Переименовать файл
     *
     * @param filePath имя файла в другом бакете
     * @param newFilePath новое имя файла
     * @param storage файловое хранилище
     */
    public void renameObject(String filePath, String newFilePath, @Nullable Storage storage)
    {
        final String localBucketName = getS3Storage(storage).getBucketName();
        moveObject(filePath, localBucketName, newFilePath, storage);
    }

    /**
     * Копировать файл
     *
     * @param filePath имя файла в другом бакете
     * @param newFilePath новое имя файла
     * @param storage файловое хранилище
     */
    public void copyObject(String filePath, String newFilePath, @Nullable Storage storage)
    {
        copyObject(filePath, getS3Storage(storage).getBucketName(), newFilePath, storage);
    }

    /**
     * Получить метаинформацию о файле в ФХ
     *
     * @param filePath имя файла
     * @param storage файловое хранилище
     * @return метаинформация
     */
    public ObjectMetadata getObjectMetadata(String filePath, @Nullable Storage storage)
    {
        final FileLocation fileLocation = new FileLocation(filePath, getS3Storage(storage).getCode());
        return s3StorageOperations.getObjectMetadata(fileLocation);
    }

    /**
     * Проверить наличие файла в ФХ
     *
     * @param filePath имя файла
     * @param storage файловое хранилище
     * @return true - есть, false - отсутствует
     */
    public boolean isObjectExists(String filePath, @Nullable Storage storage)
    {
        final FileLocation fileLocation = new FileLocation(filePath, getS3Storage(storage).getCode());
        return s3StorageOperations.isObjectExists(fileLocation);
    }

    /**
     * Скопировать файл из другого бакета
     *
     * @param filePath имя файла в другом бакете
     * @param sourceBucketName имя бакета, из которого будет копировать
     * @param newFilePath новое имя файла
     * @param storage файловое хранилище
     */
    private void copyObject(String filePath, String sourceBucketName, String newFilePath, @Nullable Storage storage)
    {
        final FileLocation newFileLocation = new FileLocation(newFilePath, getS3Storage(storage).getCode());
        s3StorageOperations.copyObject(filePath, sourceBucketName, newFileLocation);
    }

    /**
     * Удаление файла из определенного бакета
     *
     * @param filePath имя файла
     * @param destinationBucketName имя бакета
     * @param storage файловое хранилище
     */
    private void deleteObject(String filePath, String destinationBucketName, @Nullable Storage storage)
    {
        final FileLocation fileLocation = new FileLocation(filePath, getS3Storage(storage).getCode());
        s3StorageOperations.deleteObject(fileLocation, destinationBucketName);
    }

    /**
     * Получение ФХ типа S3 для файла
     *
     * @param storage файловое хранилище (если не задано, то пробуем взять активное ФХ)
     * @return ФХ типа S3 для файла
     * @throws FxException если ФХ не является S3 хранилищем
     */
    private Storage getS3Storage(@Nullable Storage storage) throws FxException
    {
        final Storage currentStorage = storage == null ? settingsService.getActiveStorage() : storage;
        if (currentStorage == null || currentStorage.getType() != StorageType.S_3)
        {
            throw new FxException(messages.getMessage("activeFileStorageAreNotS3Type"));
        }
        return currentStorage;
    }
}
