package ru.naumen.core.server.hibernate.table;

import static ru.naumen.core.server.hibernate.column.ColumnDescriptions.nullType;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;

import com.google.common.collect.Lists;

import ru.naumen.commons.server.utils.FunctionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.hibernate.column.ColumnDescription;
import ru.naumen.core.server.partition.type.PartitionKind;
import ru.naumen.metainfo.shared.elements.HasName;

/**
 * <AUTHOR>
 *         Date: 11.06.2006
 */
public class TableDescription implements HasName
{
    public static final Function<TableDescription, Collection<ColumnDescription>> toColumns = FunctionUtils
            .toCollection(TableDescription.class, "columns");

    private String nameValue;
    private boolean temporary = false;
    private List<ColumnDescription> columns = new ArrayList<>();
    private PartitionKind partitionKind = PartitionKind.NONE;

    public TableDescription(String name)
    {
        this.nameValue = name;
    }

    public TableDescription(String name, ColumnDescription... columns)
    {
        this(name, Arrays.asList(columns));
    }

    public TableDescription(String name, List<ColumnDescription> columns)
    {
        this.nameValue = name;
        setColumns(columns);
    }

    public TableDescription(String name, PartitionKind partitionKind,
            List<ColumnDescription> columns)
    {
        this(name, columns);
        this.partitionKind = partitionKind;
    }

    public TableDescription(String name, PartitionKind partitionKind)
    {
        this(name);
        this.partitionKind = partitionKind;
    }

    public TableDescription addColumn(ColumnDescription column)
    {
        columns.add(column);
        return this;
    }

    public List<ColumnDescription> getColumns()
    {
        return columns;
    }

    @Override
    public String getName()
    {
        return nameValue;
    }

    public boolean isPartition()
    {
        return partitionKind != PartitionKind.NONE;
    }

    public boolean isTemporary()
    {
        return temporary;
    }

    public TableDescription setColumns(ColumnDescription... columns)
    {
        this.columns = Lists.newArrayList(columns);
        return this;
    }

    public TableDescription setColumns(List<ColumnDescription> columns)
    {
        this.columns = Lists.newArrayList(columns);
        return this;
    }

    public void setName(String name)
    {
        this.nameValue = name;
    }

    public void setTemporary(boolean temporary)
    {
        this.temporary = temporary;
    }

    public void setPartitionKind(PartitionKind partitionKind)
    {
        this.partitionKind = partitionKind;
    }

    public PartitionKind getPartitionKind()
    {
        return partitionKind;
    }

    public ColumnDescription getPartitionColumn()
    {
        return getColumns()
                .stream()
                .filter(ColumnDescription::isPartitionColumn)
                .findFirst()
                .orElse(nullType(StringUtilities.EMPTY));
    }

    public Collection<String> getColumnNames()
    {
        return getColumns().stream()
                .map(ColumnDescription::getName)
                .toList();
    }
}
