package ru.naumen.core.server.filestorage.spi.storages.operations.groovy;

import static ru.naumen.core.shared.Constants.ServiceUsers.SCRIPT_USER;

import java.io.InputStream;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Callable;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.transaction.TransactionManager;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.FxException;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.filestorage.conf.GroovyExtendedStorage;
import ru.naumen.core.server.filestorage.spi.FileStorageSettingsService;
import ru.naumen.core.server.filestorage.spi.storages.FileHashDao;
import ru.naumen.core.server.filestorage.spi.storages.FileStorageCRUD;
import ru.naumen.core.server.filestorage.spi.storages.operations.AbstractStorageOperations;
import ru.naumen.core.server.filestorage.spi.storages.operations.FileLocation;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.spi.ScriptDtOHelper;
import ru.naumen.core.server.upload.strategies.SetContentContext;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Реализация стратегии работы с хранилищем(работа с которым осуществляется через groovy-модуль)
 * <AUTHOR>
 * @since 9 сент. 2016 г.
 */
@Component
@Lazy
public class GroovyExtendedFileStorageOperations extends AbstractStorageOperations
{
    protected final ScriptDtOHelper wrapper;
    private final ScriptService scriptService;
    private final AuthorizationRunnerService authorizeRunner;
    private final FileHashDao hashDao;
    private FileStorageCRUD fileStorage;
    private GroovyExtendedStorage settings;
    private boolean initedFS = false;

    @Inject
    protected GroovyExtendedFileStorageOperations(
            FileStorageSettingsService settingsService,
            MessageFacade messages, TransactionManager txManager, ScriptDtOHelper wrapper, ScriptService scriptService,
            AuthorizationRunnerService authorizeRunner, FileHashDao hashDao)
    {
        super(settingsService, messages, txManager);
        this.wrapper = wrapper;
        this.scriptService = scriptService;
        this.authorizeRunner = authorizeRunner;
        this.hashDao = hashDao;
    }

    @Override
    public void checkIfContentExists(File file)
    {
        if (!fileStorage.exists(getFilePath(file.getUUID())))
        {
            throw new FxException(messages.getMessage("fileDoesNotExistInStorage", file.getUUID()));
        }
    }

    @Override
    public String getFilePath(final File file)
    {
        return getFilePath(file.getUUID());
    }

    @Override
    public String getFilePath(@Nullable final String storageId, final long idForStorage,
            final boolean compressed)
    {
        return null;
    }

    @Override
    public void setContent(final SetContentContext setContentContext)
    {

    }

    public FileStorageCRUD getFileStorage()
    {
        initFileStorageCRUD();
        return fileStorage;
    }

    /**
     * инициализация настроек groovy-модуля
     * @param settings настройки groovy-модуля
     */
    public void init(GroovyExtendedStorage settings)
    {
        this.settings = settings;
        initedFS = false;
    }

    @Override
    protected void doDelete(final FileLocation fileLocation)
    {
        String filePath = fileLocation.getFilePath();
        initFileStorageCRUD();
        fileStorage.delete(filePath);
    }

    @Override
    protected InputStream doGetFileInputStream(final FileLocation fileLocation)
    {
        String filePath = fileLocation.getFilePath();
        initFileStorageCRUD();
        return fileStorage.read(filePath);
    }

    @Override
    protected void doSetContent(@Nullable final File file, @Nullable final String storageCode, final String filePath,
            final InputStream content, long contentLength, final boolean compress)
    {
        if (null == file)
        {
            return;
        }
        initFileStorageCRUD();
        DtObject fileObj = Objects.requireNonNull(wrapper.wrap(file));

        if (fileStorage.exists(filePath))
        {
            fileStorage.update(filePath, fileObj, content);
        }
        else
        {
            String id = fileStorage.create(fileObj, content);
            List<Long> files = hashDao.getFileIdsWithSameHash(file.getHash());
            authorizeRunner.callAsSuperUser(SCRIPT_USER, (Callable<Void>)() ->
            {
                for (Long fileId : files)
                {
                    fileStorage.saveFilePath(File.CLASS_ID + "$" + fileId, id);
                }
                return null;
            });
        }
    }

    /**
     *
     * @param fileUUID UUID БО Файл
     * @return Путь во внешнем хранилище
     */
    protected String getFilePath(String fileUUID)
    {
        initFileStorageCRUD();
        return authorizeRunner.callAsSuperUser(SCRIPT_USER, () -> fileStorage.getFilePath(fileUUID));
    }

    /**
     * Сохранить  идентификатор файла во внешнем хранилище в БО типа file
     * @param fileUUID идентификатор БО типа file
     * @param filePath идентификатор файла во внешнем хранилище
     */
    void saveFilePath(String fileUUID, String filePath)
    {
        initFileStorageCRUD();
        authorizeRunner.runAsSuperUser(SCRIPT_USER, () -> fileStorage.saveFilePath(fileUUID, filePath));
    }

    /**
     * Получить реализацию {@link FileStorageCRUD} из groovy-модуля
     */
    private void initFileStorageCRUD()
    {
        if (initedFS)
        {
            return;
        }
        fileStorage = authorizeRunner.callAsSuperUser("script",
                () -> scriptService.executeModuleFunction(settings.getModule(), settings.getMethod(),
                        settings.getInitParams()));
        initedFS = true;
    }

}
