package ru.naumen.core.server.templates;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.common.server.utils.html.ObsoleteHtmlSanitizer;//NOSONAR
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.metainfo.shared.dispatch2.StringXssValidatorAction;

/**
 *
 * <AUTHOR>
 * @since 18.10.2019
 *
 */
@Component
public class StringXssValidatorActionHandler extends
        TransactionalActionHandler<StringXssValidatorAction, SimpleResult<String>>
{
    @Inject
    private ObsoleteHtmlSanitizer obsoleteHtmlSanitizer;//NOSONAR
    @Inject
    private MessageFacade messages;

    @Override
    public SimpleResult executeInTransaction(StringXssValidatorAction action, ExecutionContext context)
            throws DispatchException
    {
        if (!obsoleteHtmlSanitizer.validateStringAttr(action.getValidateValue()))
        {
            return new SimpleResult(messages.getMessage("StringContainsXSSVulnerabilityClient"));
        }
        return new SimpleResult(null);
    }
}
