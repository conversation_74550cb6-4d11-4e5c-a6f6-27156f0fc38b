package ru.naumen.core.server.filestorage.spi;

import java.util.Date;
import java.util.Objects;

import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorColumn;
import jakarta.persistence.DiscriminatorType;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Inheritance;
import jakarta.persistence.InheritanceType;
import jakarta.persistence.Table;

import org.hibernate.annotations.CreationTimestamp;

/**
 * Сущность, необходимая для таблицы со списком файлов, которые необходимо перенести во внешнее хранилище
 *
 * В <code>@{@link jakarta.persistence.DiscriminatorValue}</code> наследниках нужно
 * указывать код из {@link ru.naumen.core.server.filestorage.conf.Storage.StorageType}
 *
 * Не кэшируется в кэше второго уровня т.к. это затрудняет получение актуальной информации о местоположение файла
 *
 * <AUTHOR>
 * @since 16 сент. 2016 г.
 */
@Entity
/* Аннотация @Immutable убрана сознательно! Пожертвуем производительностью ради блокировок.
   Дело в том, что в Hibernate есть баг:
   https://hibernate.atlassian.net/browse/HHH-4950#icft=HHH-4950
   или (дубликат) https://hibernate.atlassian.net/browse/HHH-7421
   Кратко: для сущности в состоянии READ_ONLY нельзя изменить блокировку или
   получить текущий режим блокировки. Можно только для MANAGED сущностей.
   Что происходит:
   Когда мы делаем session.get(), hibernate устанавливает для сущностей с аннотацией
   @Immutable статус READ_ONLY. Сначала мы читаем контент файла с блокировкой PESSIMISTIC_READ.
   Затем удаляем файл и текущую сущность, для этого ставим блокировку PESSIMISTIC_WRITE.
   Происходит смена режима блокировка, получаем некорретное исключение:
   org.hibernate.ObjectDeletedException: attempted to lock a deleted instance.
   Если мы сразу запрашиваем объект для удаления с блокировкой PESSIMISTIC_WRITE,
   то проблемы нет, т.к. нет изменения режима блокировки.
 */
@Cacheable(value = false)
@Table(name = "tbl_sys_move_to_external")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "storage", discriminatorType = DiscriminatorType.STRING)
public class MoveToExternal
{
    @Id
    @Column(name = "hash_id")
    private long fileHash;

    /**
     * Дата постановки файла в очередь на перенос
     */
    @CreationTimestamp
    @Column(name = "queued_date")
    private Date queuedDate;

    public MoveToExternal(long fileHash)
    {
        this.fileHash = fileHash;
    }

    MoveToExternal()
    {
    }

    @Override
    public boolean equals(Object obj)
    {
        if (obj == this)
        {
            return true;
        }
        if (!(obj instanceof MoveToExternal))
        {
            return false;
        }
        return Objects.equals(fileHash, ((MoveToExternal)obj).getFileHash());
    }

    @Override
    public int hashCode()
    {
        return Objects.hashCode(fileHash);
    }

    public long getFileHash()
    {
        return fileHash;
    }

    public Date getQueuedDate()
    {
        return queuedDate;
    }
}
