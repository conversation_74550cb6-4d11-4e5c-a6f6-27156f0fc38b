package ru.naumen.core.server.flex.spi;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;

import org.hibernate.boot.Metadata;

/**
 * Позволяет произвести дополнительную кастомизацию схемы базы
 *
 * <AUTHOR>
 */
public interface SchemaUpdater
{
    boolean canProcess(Object param);

    void update(Connection connection, Metadata metadata, Map<?, ?> config) throws SQLException;

    void update(Object param, Connection connection, Metadata metadata, Map<?, ?> config) throws SQLException;
}
