package ru.naumen.core.server.flex.codegen.attributes.object.timeinterval;

import static java.util.Arrays.asList;
import static java.util.Collections.singletonList;
import static ru.naumen.core.server.flex.codegen.attributes.AttributeGenConstants.COLUMN_KEY;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javassist.CtClass;
import ru.naumen.core.server.flex.attr.AttrStrategyComponent;
import ru.naumen.core.server.flex.codegen.GenContext;
import ru.naumen.core.server.flex.codegen.attributes.AttributeAppender;
import ru.naumen.core.server.flex.codegen.attributes.AttributePropertiesBuilder;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.DateTimeIntervalAttributeType;

/**
 * Генерирует необходимые классы, методы и аннотации для flex-атрибута типа "Временной интервал".
 *
 * <AUTHOR>
 * @since 18.12.2023
 */
@AttrStrategyComponent(types = { Constants.DateTimeIntervalAttributeType.CODE })
public class TimeIntervalAttributeGenerator implements AttributeAppender
{
    @Override
    public Collection<CtClass> appendProperty(Attribute attribute, CtClass persistentClass, GenContext context)
    {
        return AttributePropertiesBuilder.build(attribute,
                c -> FlexPropertyMethodsGenerator.generate(attribute, persistentClass, c),
                c -> FlexPropertyFqnMethodsGenerator.generate(attribute, persistentClass, c),
                context);
    }

    @Override
    public String getAccessor()
    {
        return Constants.Accessors.FLEX;
    }

    @Override
    public List<String> getColumnNames(Attribute attribute)
    {
        final TimeIntervalColumnsExtractor extractor = new TimeIntervalColumnsExtractor(attribute);
        final String millisecondsColumn = extractor.getMillisecondsColumnName();

        final DateTimeIntervalAttributeType dateTimeIntervalAttributeType = attribute.getType().cast();
        if (Boolean.TRUE.equals(dateTimeIntervalAttributeType.isNeedStoreUnits()))
        {
            final String intervalNameColumn = extractor.getIntervalNameColumnName();
            return asList(millisecondsColumn, intervalNameColumn);
        }
        return singletonList(millisecondsColumn);
    }

    @Override
    public boolean isSupportUnique()
    {
        return true;
    }

    @Override
    public Map<Object, Object> getCommonProperties(Attribute attribute)
    {
        Map<Object, Object> map = HashMap.newHashMap(1);
        map.put(COLUMN_KEY, new TimeIntervalColumnsExtractor(attribute).getColumnPrefix());
        return map;
    }
}
