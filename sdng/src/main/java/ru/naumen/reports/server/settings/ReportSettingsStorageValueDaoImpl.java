package ru.naumen.reports.server.settings;

import static ru.naumen.commons.shared.utils.StringUtilities.isEmpty;

import java.util.Collection;

import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.hquery.HRestrictions;

/**
 * <AUTHOR>
 * @since 14.11.2012
 */
@Component("reportSettingsStorageValueDao")
public class ReportSettingsStorageValueDaoImpl implements ReportSettingsStorageValueDao
{
    @Inject
    @Named("metastorageSessionFactory")
    SessionFactory sessionFactory;

    @Override
    @SuppressWarnings("unchecked")
    public Collection<ReportSettingsStorageValue> get(String type)
    {
        return HHelper.create(ReportSettingsStorageValue.class).createQuery(getSession()).list();
    }

    @Override
    public ReportSettingsStorageValue getForReport(String reportUuid, @Nullable String subjectUuid,
            @Nullable String userUuid)
    {
        HCriteria criteria = HHelper.create(ReportSettingsStorageValue.class);
        ReportSettingsStorageValue value = (ReportSettingsStorageValue)criteria
                .add(HRestrictions.eq(criteria.getProperty("reportUuid"), reportUuid))
                .add(isEmpty(subjectUuid) ? HRestrictions.isNull(criteria.getProperty("subjectUuid"))
                        : HRestrictions.eq(criteria.getProperty("subjectUuid"), subjectUuid))
                .add(isEmpty(userUuid) ? HRestrictions.isNull(criteria.getProperty("userUuid"))
                        : HRestrictions.eq(criteria.getProperty("userUuid"), userUuid))
                .createQuery(getSession())
                .setCacheable(true)
                .uniqueResult();
        return returnOrCreateNew(value, reportUuid, subjectUuid, userUuid);
    }

    @Override
    public long save(String reportUuid, String subjectUuid, String userUuid, String value)
    {
        ReportSettingsStorageValue storageValue = getForReport(reportUuid, subjectUuid, userUuid);
        storageValue.setValue(value);
        getSession().persist(storageValue);
        return storageValue.getId();
    }

    Session getSession()
    {
        return sessionFactory.getCurrentSession();
    }

    private ReportSettingsStorageValue returnOrCreateNew(ReportSettingsStorageValue source, String reportUuid,
            String subjectUuid, String userUuid)
    {
        //@formatter:off
        return (source != null) ? source : new ReportSettingsStorageValue()
            .setReportUuid(reportUuid)
            .setSubjectUuid(subjectUuid)
            .setUserUuid(userUuid);
        //@formatter:on
    }
}
