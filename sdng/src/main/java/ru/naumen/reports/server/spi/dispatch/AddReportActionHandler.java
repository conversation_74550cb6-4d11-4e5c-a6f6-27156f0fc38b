package ru.naumen.reports.server.spi.dispatch;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.dispatch.DtObjectService;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.reports.server.ReportsService;
import ru.naumen.reports.shared.dispatch.AddReportAction;

/**
 * <AUTHOR>
 * @since 01.10.2012
 */
@Component
public class AddReportActionHandler extends TransactionalActionHandler<AddReportAction, SimpleResult<DtObject>>
{
    private final ReportsService reportsService;
    private final DtObjectService dtObjectService;

    @Inject
    public AddReportActionHandler(ReportsService reportsService, DtObjectService dtObjectService)
    {
        this.reportsService = reportsService;
        this.dtObjectService = dtObjectService;
    }

    @Override
    public SimpleResult<DtObject> executeInTransaction(AddReportAction action, ExecutionContext context)
            throws DispatchException
    {
        String reportUUID = reportsService.addReport(action.getProperties());
        DtObject dtObj = dtObjectService.getDtObject(reportUUID, null);
        return new SimpleResult<>(dtObj);
    }
}