package ru.naumen.metainfo.shared.embeddedapplication;

import java.util.ArrayList;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * Пользовательская форма входа для мобильного приложения
 *
 * <AUTHOR>
 * @since 24.06.2021
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "CustomLoginFormApplication")
public class CustomLoginFormApplication extends EmbeddedApplication
{
    private String scriptModuleCode;

    public CustomLoginFormApplication()
    {
    }

    @Override
    public CustomLoginFormApplication clone()
    {
        CustomLoginFormApplication clone = new CustomLoginFormApplication();
        clone.code = this.code;
        clone.initialHeight = this.initialHeight;
        clone.mobileHeight = this.mobileHeight;
        clone.on = this.on;
        clone.script = this.script;
        clone.embeddedApplicationType = this.embeddedApplicationType;
        clone.fullscreenAllowed = fullscreenAllowed;
        clone.fileUuid = this.fileUuid;
        clone.clientApplicationFile = this.clientApplicationFile;
        clone.libraryName = this.libraryName;
        ObjectUtils.cloneCollection(title, clone.title = new ArrayList<>());
        ObjectUtils.cloneCollection(description, clone.description = new ArrayList<>());
        clone.scriptModuleCode = this.scriptModuleCode;
        clone.settingsSet = this.settingsSet;
        ObjectUtils.cloneCollection(usagePoints, clone.usagePoints = new ArrayList<>());
        return clone;
    }

    @Override
    public String getApplicationAddress()
    {
        return null;
    }

    public String getScriptModuleCode()
    {
        return scriptModuleCode;
    }

    public void setScriptModuleCode(@Nullable String scriptModuleCode)
    {
        this.scriptModuleCode = scriptModuleCode;
    }
}
