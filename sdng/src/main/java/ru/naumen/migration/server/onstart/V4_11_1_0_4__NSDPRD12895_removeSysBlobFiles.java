package ru.naumen.migration.server.onstart;

import java.sql.Connection;

import jakarta.inject.Inject;
import ru.naumen.core.server.hibernate.DataBaseInfo;
import ru.naumen.migration.server.JavaMigrationScript;
import ru.naumen.migration.server.metastoragescripts.SysBlobFilesRemover;

/**
 * Для БД PostgresSQL перезаписывыем контент системных файлов в byte[], данные из колонки BLOB удаляем
 *
 * <AUTHOR>
 * @since 13.09.19
 *
 **/
public class V4_11_1_0_4__NSDPRD12895_removeSysBlobFiles extends JavaMigrationScript
{
    @Inject
    private DataBaseInfo dataBaseInfo;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        SysBlobFilesRemover.removeSysBlobFiles(dataBaseInfo, connection);
    }
}
