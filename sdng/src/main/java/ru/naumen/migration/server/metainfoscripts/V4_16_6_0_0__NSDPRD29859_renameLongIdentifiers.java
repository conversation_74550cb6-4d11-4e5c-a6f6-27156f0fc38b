package ru.naumen.migration.server.metainfoscripts;

import java.sql.Connection;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.idstorage.IdsResult;
import ru.naumen.core.shared.Constants.NDAPConstants.NDAPAudit.DesynchronizationObject;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Миграция по изменению идентификаторов, имеющих длину свыше 30 символов в идентификаторы, имеющие длину не более
 * 30 символов
 * <p>
 * В рамках миграции происходит переименование таблиц "tbl_sys_ndapaudit_desync_obj_props" и
 * "tbl_sys_persistent_id_storage_for_big_criterion"
 *
 * <AUTHOR>
 * @since 31.03.2022
 */
public class V4_16_6_0_0__NSDPRD29859_renameLongIdentifiers extends JavaMigrationScript
{
    private static final Logger LOG =
            LoggerFactory.getLogger(V4_16_6_0_0__NSDPRD29859_renameLongIdentifiers.class);
    private static final String OLD_TBL_AUDIT = DDLTool.getCanonicalIdentifier("tbl_sys_ndapaudit_desync_obj_props");
    private static final String OLD_TBL_IDS_RESULT = DDLTool.getCanonicalIdentifier(
            "tbl_sys_persistent_id_storage_for_big_criterion");

    @Override
    public void migrate(Connection connection) throws Exception
    {
        DDLTool tool = new DDLTool(connection);

        if (tool.tableExists(OLD_TBL_AUDIT) && !tool.tableExists(DesynchronizationObject.PROPS_TBL_NAME))
        {
            tool.renameTable(OLD_TBL_AUDIT, DesynchronizationObject.PROPS_TBL_NAME);
            LOG.info("Table with name {} is renamed to {}", OLD_TBL_AUDIT, DesynchronizationObject.PROPS_TBL_NAME);
        }

        if (tool.tableExists(OLD_TBL_IDS_RESULT) && !tool.tableExists(IdsResult.TABLE_NAME))
        {
            tool.renameTable(OLD_TBL_IDS_RESULT, IdsResult.TABLE_NAME);
            LOG.info("Table with name {} is renamed to {}", OLD_TBL_IDS_RESULT, IdsResult.TABLE_NAME);
        }
    }
}