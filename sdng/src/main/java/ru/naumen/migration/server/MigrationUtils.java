package ru.naumen.migration.server;

import java.sql.Connection;
import java.sql.SQLException;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.hibernate.DDLTool;

import ru.naumen.core.server.hibernate.DataBaseInfo;

import jakarta.inject.Inject;

/**
 * <AUTHOR>
 * @since 28.08.2012
 */
@Component
public class MigrationUtils
{
    private static final String TBL_ROOT = "tbl_root";

    @Inject
    private DataBaseInfo dataBaseInfo;

    /**
     * @return true, если данных нет
     **/
    public boolean isEmptySchema(Connection connection) throws SQLException
    {
        DDLTool ddlTool = new DDLTool(connection);
        String tableName = dataBaseInfo.isOracle() ? TBL_ROOT.toUpperCase() : TBL_ROOT;
        return !ddlTool.tableExists(tableName);
    }
}
