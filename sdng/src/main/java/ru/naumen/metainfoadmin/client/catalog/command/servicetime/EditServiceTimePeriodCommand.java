package ru.naumen.metainfoadmin.client.catalog.command.servicetime;

import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.forms.FormDisplay;
import ru.naumen.core.client.forms.FormPresenterBase;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.catalog.impl.servicetime.ServiceTimeGinModule;

/**
 * Команда редактирования элемента графика обслуживания.
 *
 * <AUTHOR>
 * @since 27.06.2011
 */
public class EditServiceTimePeriodCommand extends ServiceTimeCommandBase<DtObject>
{
    @Inject
    ServiceTimeGinModule.EditServiceTimePeriodFormFactory formFactory;

    @Inject
    public EditServiceTimePeriodCommand(@Assisted CommandParam<DtObject, DtObject> param)
    {
        super(param);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }

    @Override
    protected FormPresenterBase<FormDisplay> getPresenter(DtObject object, String state)
    {
        return formFactory.create(object, param.getCallback(), state);
    }
}
