package ru.naumen.metainfoadmin.client.catalog.item.forms.impl.folder;

import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.Constants.FolderCatalog;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormConstants;

/**
 * <AUTHOR>
 * @since 22.12.2012
 */
public class FolderItemFormConstants extends CatalogItemFormConstants<FolderItemFormContext>
{
    // @formatter:off
    private static final String[] CODES = {
        CatalogItem.ITEM_TITLE,
        CatalogItem.ITEM_CODE,
        CatalogItem.ITEM_PARENT,
        FolderCatalog.DESCRIPTION,
        CatalogItem.SETTINGS_SET
    };
    // @formatter:on

    @Override
    public String[] propertyCodes()
    {
        return CODES;
    }
}