package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs;

import java.util.Collections;
import java.util.List;
import java.util.function.Predicate;

import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.view.client.AbstractDataProvider;
import com.google.gwt.view.client.HasData;
import com.google.gwt.view.client.RangeChangeEvent;
import com.google.inject.Inject;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDController;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDControllerFactory;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDGroupController;
import ru.naumen.core.client.listeditor.dnd.group.DataTableDnDGroupController;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.DataTable;
import ru.naumen.core.client.widgets.DefaultCellTable;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfoadmin.client.AbstractHasContextPresenter;
import ru.naumen.metainfoadmin.client.TableDisplay;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay.WithArrowsCellTableResources;
import ru.naumen.objectlist.client.ObjectListColumnBuilder;

/**
 * Базовый класс презентера со списком атрибутов, участвующего в настройке представления какого-либо списка
 * <AUTHOR>
 * @since 19 февр. 2016 г.
 *
 */
public abstract class AttributesContentPresenterBase<C extends HasCode, T, D extends TableDisplay<C>> extends
        AbstractHasContextPresenter<D>
{
    private class AttributeListSyncDnDController extends DataTableDnDGroupController
    {
        public AttributeListSyncDnDController()
        {
            super(getDisplay().getTableContainer().getElement());
        }

        @Override
        public void move(int oldPosition, int newPosition, ReadyState readyState)
        {
            moveAttribute(oldPosition, newPosition);
            eventBus.fireEvent(new RefreshDefaultPrsFormEvent());
        }
    }

    @Inject
    protected ObjectListColumnBuilder tableBuilder;
    @Inject
    protected WithArrowsCellTableResources cellTableResources;

    @Inject
    protected ButtonFactory buttonFactory;

    protected AttributesContentPresenterConfig presenterConfig = new AttributesContentPresenterConfig();
    protected ListEditorDnDController dndController;

    protected AbstractDataProvider<C> dataProvider = new AbstractDataProvider<C>()
    {
        @Override
        protected void onRangeChanged(HasData<C> display)
        {
            List<C> attrs = getAttributes();
            if (CollectionUtils.isEmpty(attrs))
            {
                display.setRowCount(0);
                display.setRowData(0, Collections.<C> emptyList());
            }
            else
            {
                display.setRowData(0, attrs);
                display.setRowCount(attrs.size());
            }
        }
    };

    protected AsyncCallback<Void> refreshCallback = new BasicCallback<Void>()
    {
        @Override
        public void handleSuccess(Void result)
        {
            refreshDisplay();
        }
    };

    protected ToolBarDisplayMediator<T> toolBar;

    @Inject
    public AttributesContentPresenterBase(D display, EventBus eventBus)
    {
        super(display, eventBus);
        toolBar = new ToolBarDisplayMediator<>(getDisplay().getToolBar());
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        RangeChangeEvent.fire(getDisplay().getTable(), getDisplay().getTable().getVisibleRange());
        Scheduler.get().scheduleDeferred(new ScheduledCommand()
        {
            @Override
            public void execute()
            {
                dndController.update();
            }
        });
    }

    protected void addActionColumn(String... commands)
    {
        CommandParam<C, Void> param = createActionColumnParam();
        tableBuilder.addActionColumn(display, param, 10, commands);
    }

    protected void addActionColumn(Predicate<C> visibleConditions, String... commands)
    {
        CommandParam<C, Void> param = createActionColumnParam();
        tableBuilder.addActionColumn(display, param, 10, visibleConditions, commands);
    }

    abstract protected void addTableColumns(DataTable<C> table);

    protected abstract CommandParam<C, Void> createActionColumnParam();

    protected ListEditorDnDGroupController createDnDGroupController()
    {
        return new AttributeListSyncDnDController();
    }

    /**
     * Набор объектов-атрибутов, отображаемых в списке
     */
    protected abstract List<C> getAttributes();

    protected void moveAttribute(int oldPosition, int newPosition)
    {
    }

    @Inject
    protected void initDnD(ListEditorDnDControllerFactory dndControllerFactory)
    {
        dndController = dndControllerFactory.create(createDnDGroupController());
    }

    protected void initLeftActionColumns()
    {
        addActionColumn(presenterConfig.getMoveAttrUpCode());
        addActionColumn(presenterConfig.getMoveAttrDownCode());
    }

    protected void initRightActionColumns()
    {
        addActionColumn(presenterConfig.getEditAttrCommandCode());
        addActionColumn(presenterConfig.getDeleteAttrCommandCode());
    }

    protected abstract void initToolBar();

    @Override
    protected void onBind()
    {
        super.onBind();
        cellTableResources.cellTableStyle().ensureInjected();
        getDisplay().setCaption(presenterConfig.getCaption());
        DebugIdBuilder.ensureDebugId(getDisplay(), "attributes");
        initToolBar();
        initTable();
    }

    private void initTable()
    {
        DataTable<C> table = getDisplay().getTable();
        table.setVisibleRange(0, DefaultCellTable.DEFAULT_PAGESIZE);

        initLeftActionColumns();

        addTableColumns(table);

        initRightActionColumns();

        dataProvider.addDataDisplay(table);
        registerHandler(table.addRangeChangeHandler(new RangeChangeEvent.Handler()
        {
            @Override
            public void onRangeChange(RangeChangeEvent event)
            {
                Scheduler.get().scheduleDeferred(new ScheduledCommand()
                {
                    @Override
                    public void execute()
                    {
                        dndController.update();
                    }
                });
            }
        }));
    }
}