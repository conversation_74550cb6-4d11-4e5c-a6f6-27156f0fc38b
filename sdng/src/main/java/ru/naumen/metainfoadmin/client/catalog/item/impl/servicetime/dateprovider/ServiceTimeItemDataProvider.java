package ru.naumen.metainfoadmin.client.catalog.item.impl.servicetime.dateprovider;

import java.util.List;

import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.gwt.view.client.HasData;
import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.ObjectService;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.utils.DtoAsyncDataProvider;
import ru.naumen.core.shared.dispatch.GetDtObjectListResponse;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.catalog.item.impl.servicetime.ServiceTimeItemContext;
import ru.naumen.metainfoadmin.client.catalog.item.impl.servicetime.dateprovider.ServiceTimeItemDataProviderGinModule.ServiceTimeItemDtObjectConverterFactory;

/**
 * <AUTHOR>
 * @since 17.10.2012
 *
 */
public class ServiceTimeItemDataProvider<T> extends DtoAsyncDataProvider
{
    private final ObjectService objectService;
    private final ServiceTimeItemDtObjectConverterFactory<T> dtObjectConverterFactory;
    private final ServiceTimeItemIntervalValuesExtractor<T> intervalValuesExtractor;
    protected final ServiceTimeItemContext context;

    @Inject
    public ServiceTimeItemDataProvider(ObjectService objectService,
            ServiceTimeItemDtObjectConverterFactory<T> dtObjectConverterFactory,
            ServiceTimeItemIntervalValuesExtractor<T> intervalValuesExtractor,
            @Assisted ServiceTimeItemContext context)
    {
        this.objectService = objectService;
        this.dtObjectConverterFactory = dtObjectConverterFactory;
        this.intervalValuesExtractor = intervalValuesExtractor;
        this.context = context;
    }

    @Override
    protected void onRangeChanged(HasData<DtObject> data)
    {
        objectService.getDtObjectList(getDtoCriteria(),
                new BasicCallback<GetDtObjectListResponse>(context.getDisplay())
                {
                    @Override
                    protected void handleSuccess(GetDtObjectListResponse value)
                    {
                        if (!value.getObjects().isEmpty())
                        {
                            context.setCatalogItem(value.getObjects().get(0));
                            //м.б. сортировочку?
                            List<DtObject> objects = Lists.newArrayList(Collections2.transform(
                                    intervalValuesExtractor.apply(context), dtObjectConverterFactory.create(context)));
                            updateRowCount(objects.size(), true);
                            updateRowData(0, objects);
                        }
                    }
                });
    }
}