package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.embeddedapplications.columns;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.columns.ClickableTextColumn;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplicationType;
import ru.naumen.metainfoadmin.client.embeddedapplications.EmbeddedApplicationMessages;

/**
 * <AUTHOR>
 * @since 07.07.16
 */
public class EmbeddedApplicationTypeColumn extends ClickableTextColumn<DtObject>
{
    @Inject
    protected EmbeddedApplicationMessages messages;

    @Override
    public String getValue(DtObject appDto)
    {
        EmbeddedApplicationAdminSettingsDto application = appDto
                .getProperty(ru.naumen.metainfo.shared.embeddedapplication.Constants.Application.ORIGINAL_APPLICATION);

        if (application.getEmbeddedApplicationType() == EmbeddedApplicationType.ExternalApplication)
        {
            return messages.applicationHostedOnExternalServer();
        }
        else if (application.getEmbeddedApplicationType() == EmbeddedApplicationType.InternalApplication)
        {
            return messages.applicationHostedOnInternalServer();
        }
        else if (application.getEmbeddedApplicationType() == EmbeddedApplicationType.ClientSideApplication)
        {
            return messages.applicationNoServer();
        }
        else if (application.getEmbeddedApplicationType() == EmbeddedApplicationType.CustomLoginFormApplication)
        {
            return messages.applicationCustomLoginForm();
        }

        return application.getEmbeddedApplicationType().toString();
    }

    @Inject
    protected void initColumn(EmbeddedApplicationFieldUpdater<String> fieldUpdater, WidgetResources resources)
    {
        setFieldUpdater(fieldUpdater);
        setCellStyleNames(new StringBuilder(resources.additional().tableCell()).append(' ')
                .append(resources.additional().cursorPointer()).toString());

    }
}
