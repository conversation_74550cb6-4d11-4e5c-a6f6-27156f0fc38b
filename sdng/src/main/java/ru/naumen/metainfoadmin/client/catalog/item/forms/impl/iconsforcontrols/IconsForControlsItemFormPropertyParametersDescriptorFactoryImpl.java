package ru.naumen.metainfoadmin.client.catalog.item.forms.impl.iconsforcontrols;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.Constants.IconsForControlsCatalog;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormPropertyParametersDescriptorFactoryImpl;

/**
 * Регистрация свойств с параметрами
 * <AUTHOR>
 * @since 21.07.2015
 */
public class IconsForControlsItemFormPropertyParametersDescriptorFactoryImpl<F extends ObjectForm> extends
        CatalogItemFormPropertyParametersDescriptorFactoryImpl<IconsForControlsItemFormContext, F>
{
    @Override
    protected void build()
    {
        super.build();
        //@formatter:off
        registerOrModifyProperty(CatalogItem.ITEM_ICON,  cmessages.iconOriginal(), true, CatalogItem.ITEM_ICON,  3, true, true);
        registerOrModifyProperty(IconsForControlsCatalog.ICON_ORIGINAL,  cmessages.icon() + ":",
                false, IconsForControlsCatalog.ICON_ORIGINAL,  4, false, false);
        registerOrModifyProperty(Constants.CatalogItem.SETTINGS_SET,   adminDialogMessages.settingsSet(),         false,
                "settingsSet", 5, true, false);
        //@formatter:on
    }
}