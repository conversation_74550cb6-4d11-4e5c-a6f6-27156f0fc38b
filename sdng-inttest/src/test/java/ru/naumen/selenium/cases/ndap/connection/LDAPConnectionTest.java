package ru.naumen.selenium.cases.ndap.connection;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.equalTo;

import org.apache.http.HttpStatus;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.NdapConstants;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.ndap.connection.DAOConnection;
import ru.naumen.selenium.casesutil.model.ndap.connection.DAONDAPConnectionCase;
import ru.naumen.selenium.casesutil.ndap.DSLNDAP;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractNDAPTestCase;

/**
 * Тестирование подключения к LDAP
 *
 * <AUTHOR>
 * @since Sep 16, 2024
 */
public class LDAPConnectionTest extends AbstractNDAPTestCase
{
    private static ContentForm listConnections;

    /**
     * <ol>
     * <b>Общая подготовка:</b>
     * <li>Загрузить на стенд лиц файл 'Лицензионный файл с модулем NDAP'</li>
     * <li>Добавить на карточку компании контент Список объектов класса 'Подключение'</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        MetaClass rootClass = DAORootClass.create();
        MetaClass ndapConnectionClass = DAONDAPConnectionCase.createClass();
        listConnections = DAOContentCard.createObjectAdvList(rootClass.getFqn(), ndapConnectionClass);
        DSLContent.add(listConnections);
    }

    /**
     * Тестирование создания, редактирования, удаления подключения LDAP
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00649
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$273331474
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Создать LDAP подключение ldapConnection, заполнив атрибуты:
     * <pre>Название, URL, Тип аутентификации, Идентификатор пользователя, Пароль, Протокол безопасности, Таймаут</pre>
     * </li>
     * <li>Сделать запрос в NDAP получить соответствующее подключение и проверить значение полей:
     * <pre>
     *  "url": "ldap://example.ru"
     *  "authentication": "SIMPLE"
     *  "principal": "uid=admin"
     *  "password": "secret"
     *  "securityProtocol": "TLS"
     *  "timeout": 3000
     * </pre></li>
     * <li>Открыть форму редактирования ldapConnection и заполнить атрибуты
     * URL, Тип аутентификации, Идентификатор пользователя, Протокол безопасности</li>
     * <li>Сделать запрос в NDAP получить соответствующее подключение и проверить значение полей:
     * <pre>
     *  "url": "ldaps://example.com"
     *  "authentication": null
     *  "principal": "uid=operator"
     *  "securityProtocol": "SSL"
     * </pre></li>
     * <li>Удалить из карточки объект ldapConnection</li>
     * <li>Сделать запрос в NDAP, проверить, что подключение с заданным UUID в NDAP отсутствует</li>
     * </ol>
     */
    @Test
    public void testCreateUpdateDeleteLdapConnection()
    {
        Bo ldapConnection = DAOConnection.createLDAP(
                NdapConstants.LDAP_URL_EXAMPLE, "SIMPLE", "uid=admin", "secret", "TLS", 3000L);
        DSLBo.add(ldapConnection);

        //@formatter:off
        given()
            .pathParam(NdapConstants.F_ENDPOINT_NAME, ldapConnection.getUuid())
        .expect()
            .statusCode(HttpStatus.SC_OK)
            .body(NdapConstants.F_URL, equalTo(NdapConstants.LDAP_URL_EXAMPLE))
            .body(NdapConstants.F_AUTHENTICATION, equalTo("SIMPLE"))
            .body(NdapConstants.F_PRINCIPAL, equalTo("uid=admin"))
            .body(NdapConstants.F_PASSWORD, equalTo("secret"))
            .body(NdapConstants.F_SECURITY_PROTOCOL, equalTo("TLS"))
            .body(NdapConstants.F_TIMEOUT, equalTo(3000))
        .when()
            .get(NdapConstants.ENDPOINT_URL);
        //@formatter:on

        GUILogon.asTester();
        GUIBo.goToEditForm(ldapConnection);

        GUIForm.fillAttribute(SystemAttrEnum.SYSTEM_URL.getCode(), NdapConstants.LDAP_URL_EXAMPLE2);
        GUISelect.selectEmpty(GUIXpath.InputComplex.ANY_VALUE, SystemAttrEnum.SYSTEM_AUTHENTICATION.getCode());
        GUIForm.fillAttribute(SystemAttrEnum.SYSTEM_PRINCIPAL.getCode(), "uid=operator");
        GUISelect.selectByTitle(GUIXpath.InputComplex.ANY_VALUE, "SSL",
                SystemAttrEnum.SYSTEM_SECURITY_PROTOCOL.getCode());

        GUIForm.applyForm();

        //@formatter:off
        given()
            .pathParam(NdapConstants.F_ENDPOINT_NAME, ldapConnection.getUuid())
        .expect()
            .statusCode(HttpStatus.SC_OK)
                .body(NdapConstants.F_URL, equalTo(NdapConstants.LDAP_URL_EXAMPLE2))
                .body(NdapConstants.F_AUTHENTICATION, equalTo(null))
                .body(NdapConstants.F_PRINCIPAL, equalTo("uid=operator"))
                .body(NdapConstants.F_SECURITY_PROTOCOL, equalTo("SSL"))
        .when()
            .get(NdapConstants.ENDPOINT_URL);
        //@formatter:on

        GUIBo.delete(ldapConnection);

        DSLNDAP.assertEndpointAbsence(ldapConnection.getUuid());
    }

}
