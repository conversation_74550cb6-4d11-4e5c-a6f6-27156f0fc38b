package ru.naumen.selenium.cases.script.attrs.possiblevalues;

import static ru.naumen.selenium.casesutil.model.attr.AttributeConstant.CatalogItemsType.EDIT_TREE;
import static ru.naumen.selenium.casesutil.scripts.attrs.AttributePossibleValuesParams.withParams;
import static ru.naumen.selenium.casesutil.scripts.attrs.validators.PossibleValueValidators.dtoValue;

import java.util.List;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSearch;
import ru.naumen.selenium.casesutil.catalog.DSLCatalog;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.attrs.DSLAttributesApi;
import ru.naumen.selenium.core.AbstractTestCase;

/**
 * Тесты на метод API <code>api.attrs.listPossibleValues</code>, позволяющий получить возможные значения атрибута.
 * Тестируется получение возможных значений для справочников.
 *
 * <AUTHOR>
 * @since 19.05.2023
 */
public class AttributesApiCatalogItemPossibleValuesTest extends AbstractTestCase
{
    private static Attribute catalogItemsAttr, catalogItemsTreeAttr;
    private static Bo userBo;
    private static Catalog catalog;
    private static String searchPrefix;
    private static CatalogItem catalogFolder, catalogItem, catalogItem2, catalogItem3;

    /**
     * <ol>
     * <b>Общая подготовка.</b>
     * <li>Создать пользовательский класс <code>userClass</code> и его подтип <code>userCase</code></li>
     * <li>Создать справочник <code>catalog</code></li>
     * <li>Создать атрибут <code>catalogItemsAttr</code> типа "Набор элементов справочника" в типе
     * <code>userCase</code>, ссылающийся на справочник <code>catalog</code></li>
     * <li>Создать атрибут <code>catalogItemsTreeAttr</code> типа "Набор элементов справочника" в типе
     * <code>userCase</code>, ссылающийся на справочник <code>catalog</code>, с представлением "Дерево выбора"</li>
     * <li>Создать объект <code>userBo</code> типа <code>userCase</code></li>
     * <li>Создать в справочнике <code>catalog</code> папку <code>catalogFolder</code></li>
     * <li>Создать в справочнике <code>catalog</code> элементы <code>catalogItem</code>, <code>catalogItem2</code> и
     * вложенный в папку <code>catalogFolder</code> элемент <code>catalogItem3</code></li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        catalog = DAOCatalog.createUser(true, true);
        DSLCatalog.add(catalog);

        catalogItemsAttr = DAOAttribute.createCatalogItemSet(userCase, catalog);
        catalogItemsTreeAttr = DAOAttribute.createCatalogItemSet(userCase, catalog);
        catalogItemsTreeAttr.setEditPresentation(EDIT_TREE);
        DSLAttribute.add(catalogItemsAttr, catalogItemsTreeAttr);

        userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        searchPrefix = ModelUtils.createTitle(4);
        catalogFolder = DAOCatalogItem.createUserFolder(catalog);
        catalogItem = DAOCatalogItem.createUser(catalog);
        catalogItem2 = DAOCatalogItem.createUser(catalog);
        DAOCatalogItem.appendCodePrefixes(0, 4, catalogFolder, catalogItem, catalogItem2);
        DAOCatalogItem.appendTitlePrefixes(searchPrefix, 0, 4, catalogFolder, catalogItem, catalogItem2);
        DSLCatalogItem.add(catalogFolder, catalogItem, catalogItem2);

        catalogItem3 = DAOCatalogItem.createUser(catalog, catalogFolder);
        DAOCatalogItem.appendCodePrefixes(3, 4, catalogItem3);
        DAOCatalogItem.appendTitlePrefixes(searchPrefix, 3, 4, catalogItem3);
        DSLCatalogItem.add(catalogItem3);
        DSLSearch.updateIndex(catalogFolder, catalogItem, catalogItem2, catalogItem3);
    }

    /**
     * Тестирование получения через API-метод <code>api.attrs.listPossibleValues<code/> возможных значений атрибута,
     * ссылающегося на справочник.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$283489816
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Получить список возможных значений атрибута <code>catalogItemsAttr</code> объекта <code>userBo</code> через
     * скриптовое API <code>api.attrs.listPossibleValues</code></li>
     * <li>Проверить, что вернулись элементы <code>catalogItem</code>, <code>catalogItem2</code> и
     * <code>catalogItem3</code>, доступные для выбора и не имеющие дочерних элементов</li>
     * </ol>
     */
    @Test
    public void testPossibleValues()
    {
        List<Object> result = DSLAttributesApi.listPossibleValues(catalogItemsAttr, userBo);
        DSLAttributesApi.assertPossibleValues(List.of(
                dtoValue(catalogItem),
                dtoValue(catalogItem2),
                dtoValue(catalogItem3)
        ), result);
    }

    /**
     * Тестирование получения через API-метод <code>api.attrs.listPossibleValues<code/> возможных значений атрибута,
     * ссылающегося на справочник и имеющего древовидное представление.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$283489816
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Получить список возможных значений атрибута <code>catalogItemsTreeAttr</code> объекта <code>userBo</code>
     * через скриптовое API <code>api.attrs.listPossibleValues</code></li>
     * <li>Проверить, что вернулись:
     * <ul>
     *     <li>папка <code>catalogFolder</code>, не доступная для выбора и имеющая дочерние элементы;</li>
     *     <li>элементы <code>catalogItem</code> и <code>catalogItem2</code>, доступные для выбора и не имеющие дочерних
     *     элементов</li>
     * </ul></li>
     * <li>Выполнить поиск возможных значений, выполнив аналогичный вызов API c указанием родителя -
     * <code>userFolder</code></li>
     * <li>Проверить, что вернулся элемент <code>catalogItem3</code>, доступный для выбора и не имеющий дочерних
     * элементов</li>
     * </ol>
     */
    @Test
    public void testPossibleValuesWhenTreePresentation()
    {
        List<Object> result = DSLAttributesApi.listPossibleValues(catalogItemsTreeAttr, userBo);
        DSLAttributesApi.assertPossibleValues(List.of(
                dtoValue(catalogFolder).selectable(false).leaf(false),
                dtoValue(catalogItem),
                dtoValue(catalogItem2)
        ), result);

        List<Object> result2 = DSLAttributesApi.listPossibleValues(catalogItemsTreeAttr, userBo,
                withParams().parent(catalogFolder)
        );
        DSLAttributesApi.assertPossibleValues(List.of(
                dtoValue(catalogItem3)
        ), result2);
    }

    /**
     * Тестирование получения через API-метод <code>api.attrs.listPossibleValues<code/> возможных значений атрибута,
     * ссылающегося на справочник, с указанием максимального числа элементов для получения.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$283489816
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Получить список возможных значений атрибута <code>catalogItemsAttr</code> объекта <code>userBo</code> через
     * скриптовое API <code>api.attrs.listPossibleValues</code>, с указанием максимального числа получаемых
     * элементов = 1</li>
     * <li>Проверить, что вернулся элемент <code>catalogItem</code>, доступный для выбора и не имеющий дочерних
     * элементов</li>
     * <li>Выполнить поиск возможных значений, выполнив аналогичный вызов API c указанием максимального числа
     * получаемых элементов = 1</li>
     * <li>Проверить, что вернулся элемент <code>catalogItem</code>, доступный для выбора и не имеющий дочерних
     * элементов</li>
     * </ol>
     */
    @Test
    public void testPossibleValuesWithLimit()
    {
        List<Object> result = DSLAttributesApi.listPossibleValues(catalogItemsAttr, userBo, withParams().limit(1));
        DSLAttributesApi.assertPossibleValues(List.of(
                dtoValue(catalogItem)
        ), result);

        List<Object> result2 = DSLAttributesApi.listPossibleValues(catalogItemsAttr, userBo,
                withParams().limit(1).searchString(searchPrefix)
        );
        DSLAttributesApi.assertPossibleValues(List.of(
                dtoValue(catalogItem)
        ), result2);
    }

    /**
     * Тестирование получения через API-метод <code>api.attrs.listPossibleValues<code/> возможных значений атрибута,
     * ссылающегося на справочник и имеющего древовидное представление, с указанием максимального числа элементов для
     * получения.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$283489816
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Получить список возможных значений атрибута <code>catalogItemsTreeAttr</code> объекта <code>userBo</code>
     * через скриптовое API <code>api.attrs.listPossibleValues</code>, с указанием максимального числа получаемых
     * элементов = 1</li>
     * <li>Проверить, что вернулась папка <code>catalogFolder</code>, не доступная для выбора и имеющая дочерние
     * элементы</li>
     * <li>Выполнить поиск возможных значений, выполнив аналогичный вызов API c указанием максимального числа
     * получаемых элементов = 1</li>
     * <li>Проверить, что вернулось два элемента справочника (ожидаемое поведение)</li>
     * </ol>
     */
    @Test
    public void testPossibleValuesWithLimitWhenTreePresentation()
    {
        List<Object> result = DSLAttributesApi.listPossibleValues(catalogItemsTreeAttr, userBo, withParams().limit(1));
        DSLAttributesApi.assertPossibleValues(List.of(
                dtoValue(catalogFolder).selectable(false).leaf(false)
        ), result);

        List<Object> result2 = DSLAttributesApi.listPossibleValues(catalogItemsTreeAttr, userBo,
                withParams().limit(1).searchString(searchPrefix)
        );
        Assert.assertEquals(2, result2.size());
    }

    /**
     * Тестирование получения через API-метод <code>api.attrs.listPossibleValues<code/> возможных значений атрибута,
     * ссылающегося на справочник, с указанием сдвига относительно начала списка найденных элементов.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$283489816
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Получить список возможных значений атрибута <code>catalogItemsAttr</code> объекта <code>userBo</code> через
     * скриптовое API <code>api.attrs.listPossibleValues</code>, с заданным сдвигом относительно начала списка
     * найденных элементов = 1</li>
     * <li>Проверить, что вернулись элементы <code>catalogItem2</code> и <code>catalogItem3</code>, доступные для
     * выбора и не имеющие дочерних элементов</li>
     * <li>Выполнить поиск возможных значений, выполнив аналогичный вызов API с заданным сдвигом относительно начала
     * списка найденных элементов = 1</li>
     * <li>Проверить, что вернулась ошибка о том, что поиск не возможно использовать вместе с указанием сдвига</li>
     * </ol>
     */
    @Test
    public void testPossibleValuesWithOffsetMoreThenZero()
    {
        List<Object> result = DSLAttributesApi.listPossibleValues(catalogItemsAttr, userBo, withParams().offset(1));
        DSLAttributesApi.assertPossibleValues(List.of(
                dtoValue(catalogItem2),
                dtoValue(catalogItem3)
        ), result);

        DSLAttributesApi.assertPossibleValuesError(
                "All possible objects are returned for the search on the first request",
                catalogItemsAttr, userBo, withParams().offset(1).searchString(searchPrefix)
        );
    }

    /**
     * Тестирование получения через API-метод <code>api.attrs.listPossibleValues<code/> возможных значений атрибута,
     * ссылающегося на справочник и имеющего древовидное представление, с указанием и максимального числа элементов
     * для получения и сдвига относительно начала списка найденных элементов.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$283489816
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Получить список возможных значений атрибута <code>catalogItemsTreeAttr</code> объекта <code>userBo</code>
     * через скриптовое API <code>api.attrs.listPossibleValues</code>, с указанием сдвига относительно начала списка
     * папок = 1</li>
     * <li>Проверить, что вернулись элементы <code>catalogItem</code> и <code>catalogItem2</code>, доступные для
     * выбора и не имеющие дочерних элементов</li>
     * <li>Выполнить поиск возможных значений, выполнив аналогичный вызов API с указанием сдвига относительно начала
     * списка папок = 1</li>
     * <li>Проверить, что вернулась ошибка о том, что поиск не возможно использовать вместе с указанием сдвига</li>
     * </ol>
     */
    @Test
    public void testPossibleValuesWithOffsetMoreThenZeroWhenTreePresentation()
    {
        List<Object> result = DSLAttributesApi.listPossibleValues(catalogItemsTreeAttr, userBo,
                withParams().addTypeOffset(catalog, 1)
        );
        DSLAttributesApi.assertPossibleValues(List.of(
                dtoValue(catalogItem),
                dtoValue(catalogItem2)
        ), result);

        DSLAttributesApi.assertPossibleValuesError(
                "All possible objects are returned for the search on the first request",
                catalogItemsTreeAttr, userBo, withParams().addTypeOffset(catalog, 1).searchString(searchPrefix)
        );
    }

    /**
     * Тестирование получения через API-метод <code>api.attrs.listPossibleValues<code/> возможных значений атрибута,
     * ссылающегося на справочник и имеющего древовидное представление, с указанием и максимального числа элементов
     * для получения и сдвига относительно начала списка найденных элементов.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$283489816
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Получить список возможных значений атрибута <code>catalogItemsAttr</code> объекта <code>userBo</code> через
     * скриптовое API <code>api.attrs.listPossibleValues</code>, с указанием и максимального числа получаемых
     * элементов = 1 и сдвига относительно начала списка найденных элементов = 1</li>
     * <li>Проверить, что вернулся элемент <code>catalogItem2</code>, доступный для выбора и не имеющих дочерних
     * элементов</li>
     * </ol>
     */
    @Test
    public void testPossibleValuesWithOffsetMoreThenZeroAndLimit()
    {
        List<Object> result = DSLAttributesApi.listPossibleValues(catalogItemsAttr, userBo,
                withParams().offset(1).limit(1)
        );
        DSLAttributesApi.assertPossibleValues(List.of(
                dtoValue(catalogItem2)
        ), result);
    }

    /**
     * Тестирование получения через API-метод <code>api.attrs.listPossibleValues<code/> возможных значений атрибута,
     * ссылающегося на справочник и имеющего древовидное представление, с указанием и максимального числа элементов для
     * получения и сдвига относительно начала списка найденных элементов.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$283489816
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Получить список возможных значений атрибута <code>catalogItemsTreeAttr</code> объекта <code>userBo</code>
     * через
     * скриптовое API <code>api.attrs.listPossibleValues</code>, с указанием и максимального числа получаемых
     * элементов = 1, и сдвига относительно начала найденных элементов справочника catalog = 1</li>
     * <li>Проверить, что вернулся элемент <code>catalogItem</code>, доступный для выбора и не имеющих дочерних
     * элементов</li>
     * </ol>
     */
    @Test
    public void testPossibleValuesWithOffsetMoreThenZeroAndLimitWhenTreePresentation()
    {
        List<Object> result = DSLAttributesApi.listPossibleValues(catalogItemsTreeAttr, userBo,
                withParams().addTypeOffset(catalog, 1).limit(1)
        );
        DSLAttributesApi.assertPossibleValues(List.of(
                dtoValue(catalogItem)
        ), result);
    }
}
