package ru.naumen.selenium.cases.script.attrs.possiblevalues;

import static ru.naumen.selenium.casesutil.model.attr.AttributeConstant.CaseListType.CASE_LIST_TREE;
import static ru.naumen.selenium.casesutil.scripts.attrs.AttributePossibleValuesParams.withParams;
import static ru.naumen.selenium.casesutil.scripts.attrs.validators.PossibleValueValidators.metaClassValue;
import static ru.naumen.selenium.util.Json.GSON;

import java.util.List;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.metaclass.DAOMetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.attrs.DSLAttributesApi;
import ru.naumen.selenium.core.AbstractTestCase;

/**
 * Тесты на метод API <code>api.attrs.listPossibleValues</code>, позволяющий получить возможные значения атрибута.
 * Тестируется получение возможных значений типа "Набор типов класса".
 *
 * <AUTHOR>
 * @since 04.12.2024
 */
public class AttributesApiCaseListPossibleValuesTest extends AbstractTestCase
{
    private static MetaClass userCase, userCase2, userCase3, userCase4;
    private static Attribute caseListAttr, caseListTreeAttr;
    private static Bo userBo;
    private static String searchPrefix;

    /**
     * <ol>
     * <b>Общая подготовка.</b>
     * <li>Создать пользовательский класс <code>userClass</code> и его подтипы <code>userCase</code>,
     * <code>userCase2</code>, <code>userCase3</code></li>
     * <li>Создать в типе <code>userCase3</code> подтип <code>userCase4</code></li>
     * <li>Создать атрибут <code>caseListAttr</code> типа "Набор типов класса" в типе <code>userCase</code>,
     * ссылающийся на класс <code>userClass</code></li>
     * <li>Создать атрибут <code>caseListTreeAttr</code> типа "Набор типов класса" в типе <code>userCase</code> с
     * представлением "Дерево выбора", ссылающийся на класс <code>userClass</code></li>
     * <li>Создать объект <code>userBo</code> типа <code>userCase</code></li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        searchPrefix = ModelUtils.createTitle();

        MetaClass userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        userCase2 = DAOUserCase.create(userClass);
        userCase3 = DAOUserCase.create(userClass);
        DAOMetaClass.appendTitlePrefixes(searchPrefix, 0, 4, userCase);
        DAOMetaClass.appendTitlePrefixes(1, 4, userCase2, userCase3);
        DSLMetainfo.add(userClass, userCase, userCase2, userCase3);

        userCase4 = DAOUserCase.create(userCase3);
        DAOMetaClass.appendTitlePrefixes(searchPrefix, 3, 4, userCase4);
        DSLMetainfo.add(userCase4);

        caseListAttr = DAOAttribute.createCaseList(userCase, userClass);
        caseListTreeAttr = DAOAttribute.createCaseList(userCase, userClass);
        caseListTreeAttr.setEditPresentation(CASE_LIST_TREE);
        DSLAttribute.add(caseListAttr, caseListTreeAttr);

        userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
    }

    /**
     * Тестирование получения через API-метод <code>api.attrs.listPossibleValues<code/> возможных значений атрибута
     * типа "Набор типов класса".
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$283489816
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Получить список возможных значений атрибута <code>caseListAttr</code> объекта <code>userBo</code> через
     * скриптовое API <code>api.attrs.listPossibleValues</code></li>
     * <li>Проверить, что вернулись типы <code>userCase</code>, <code>userCase2</code>, <code>userCase3</code> и
     * <code>userCase4</code>, доступные для выбора и не имеющие дочерних элементов</li>
     * </ol>
     */
    @Test
    public void testPossibleValues()
    {
        List<Object> result = DSLAttributesApi.listPossibleValues(caseListAttr, userBo);
        DSLAttributesApi.assertPossibleValues(List.of(
                metaClassValue(userCase),
                metaClassValue(userCase2),
                metaClassValue(userCase3),
                metaClassValue(userCase4)
        ), result);
    }

    /**
     * Тестирование получения через API-метод <code>api.attrs.listPossibleValues<code/> возможных значений атрибута
     * типа "Набор типов класса" с древовидным представлением.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$283489816
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Получить список возможных значений атрибута <code>caseListTreeAttr</code> объекта <code>userBo</code> через
     * скриптовое API <code>api.attrs.listPossibleValues</code></li>
     * <li>Проверить, что вернулись:
     * <ul>
     *     <li>типы <code>userCase</code> и <code>userCase2</code>, доступные для выбора и не имеющие дочерних
     *     элементов;</li>
     *     <li>тип <code>userCase3</code>, доступный для выбора и имеющий дочерние элементы</li>
     * </ul></li>
     * <li>Выполнить поиск возможных значений, выполнив аналогичный вызов API c указанием родителя -
     * <code>userCase3</code></li>
     * <li>Проверить, что вернулся тип <code>userCase4</code>, доступный для выбора и не имеющий дочерних
     * элементов</li>
     * </ol>
     */
    @Test
    public void testPossibleValuesWhenTreePresentation()
    {
        List<Object> result = DSLAttributesApi.listPossibleValues(caseListTreeAttr, userBo);
        DSLAttributesApi.assertPossibleValues(List.of(
                metaClassValue(userCase),
                metaClassValue(userCase2),
                metaClassValue(userCase3).leaf(false)
        ), result);

        List<Object> result2 = DSLAttributesApi.listPossibleValues(caseListTreeAttr, userBo,
                withParams().parent(userCase3)
        );
        DSLAttributesApi.assertPossibleValues(List.of(
                metaClassValue(userCase4)
        ), result2);
    }

    /**
     * Тестирование получения через API-метод <code>api.attrs.listPossibleValues<code/> возможных значений атрибута
     * типа "Набор типов класса", с указанием максимального числа элементов для получения.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$283489816
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Получить список возможных значений атрибута <code>caseListAttr</code> объекта <code>userBo</code> через
     * скриптовое API <code>api.attrs.listPossibleValues</code>, с указанием максимального числа получаемых
     * элементов = 1</li>
     * <li>Проверить, что вернулся тип <code>userCase</code>, доступный для выбора и не имеющий дочерних элементов</li>
     * <li>Выполнить поиск возможных значений, выполнив аналогичный вызов API c указанием максимального числа
     * получаемых элементов = 1</li>
     * <li>Проверить, что вернулся тип <code>userCase</code>, доступный для выбора и не имеющий дочерних элементов</li>
     * </ol>
     */
    @Test
    public void testPossibleValuesWithLimit()
    {
        List<Object> result = DSLAttributesApi.listPossibleValues(caseListAttr, userBo, withParams().limit(1));
        DSLAttributesApi.assertPossibleValues(List.of(
                metaClassValue(userCase)
        ), result);

        List<Object> result2 = DSLAttributesApi.listPossibleValues(caseListAttr, userBo,
                withParams().limit(1).searchString(searchPrefix)
        );
        DSLAttributesApi.assertPossibleValues(List.of(
                metaClassValue(userCase)
        ), result2);
    }

    /**
     * Тестирование получения через API-метод <code>api.attrs.listPossibleValues<code/> возможных значений атрибута
     * типа "Набор типов класса" с древовидным представлением, с указанием максимального числа элементов для
     * получения.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$283489816
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Получить список возможных значений атрибута <code>caseListTreeAttr</code> объекта <code>userBo</code>
     * через скриптовое API <code>api.attrs.listPossibleValues</code>, с указанием максимального числа получаемых
     * элементов = 1</li>
     * <li>Проверить, что вернулся тип <code>userCase</code>, доступный для выбора и не имеющий дочерних элементов</li>
     * <li>Выполнить поиск возможных значений, выполнив аналогичный вызов API c указанием максимального числа
     * получаемых элементов = 1</li>
     * <li>Проверить, что вернулось два типа (ожидаемое поведение)</li>
     * </ol>
     */
    @Test
    public void testPossibleValuesWithLimitWhenTreePresentation()
    {
        List<Object> result = DSLAttributesApi.listPossibleValues(caseListTreeAttr, userBo, withParams().limit(1));
        DSLAttributesApi.assertPossibleValues(List.of(
                metaClassValue(userCase)
        ), result);

        List<Object> result2 = DSLAttributesApi.listPossibleValues(caseListTreeAttr, userBo,
                withParams().limit(1).searchString(searchPrefix)
        );
        Assert.assertEquals("Неожиданный результат:" + GSON.toJson(result2), 2, result2.size());
    }

    /**
     * Тестирование получения через API-метод <code>api.attrs.listPossibleValues<code/> возможных значений атрибута
     * типа "Набор типов класса", с указанием сдвига относительно начала списка найденных элементов.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$283489816
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Получить список возможных значений атрибута <code>caseListAttr</code> объекта <code>userBo</code> через
     * скриптовое API <code>api.attrs.listPossibleValues</code>, с заданным сдвигом относительно начала списка
     * найденных элементов = 1</li>
     * <li>Проверить, что вернулись типы <code>userCase2</code>, <code>userCase3</code> и <code>userCase4</code>,
     * доступные для выбора и не имеющие дочерних элементов</li>
     * <li>Выполнить поиск возможных значений, выполнив аналогичный вызов API с заданным сдвигом относительно начала
     * списка найденных элементов = 1</li>
     * <li>Проверить, что вернулась ошибка о том, что поиск не возможно использовать вместе с указанием сдвига</li>
     * </ol>
     */
    @Test
    public void testPossibleValuesWithOffsetMoreThenZero()
    {
        List<Object> result = DSLAttributesApi.listPossibleValues(caseListAttr, userBo, withParams().offset(1));
        DSLAttributesApi.assertPossibleValues(List.of(
                metaClassValue(userCase2),
                metaClassValue(userCase3),
                metaClassValue(userCase4)
        ), result);

        DSLAttributesApi.assertPossibleValuesError(
                "All possible objects are returned for the search on the first request",
                caseListAttr, userBo, withParams().offset(1).searchString(searchPrefix)
        );
    }

    /**
     * Тестирование получения через API-метод <code>api.attrs.listPossibleValues<code/> возможных значений атрибута
     * типа "Набор типов класса" с древовидным представлением, с указанием и максимального числа элементов
     * для получения и сдвига относительно начала списка найденных элементов.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$283489816
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Получить список возможных значений атрибута <code>caseListTreeAttr</code> объекта <code>userBo</code>
     * через скриптовое API <code>api.attrs.listPossibleValues</code>, с указанием сдвига относительно начала списка
     * папок = 1</li>
     * <li>Проверить, что вернулись:
     * <ul>
     *     <li>тип <code>userCase2</code>, доступный для выбора и не имеющий дочерних элементов;</li>
     *     <li>тип <code>userCase3</code>, доступный для выбора и имеющий дочерние элементы</li>
     * </ul></li>
     * <li>Выполнить поиск возможных значений, выполнив аналогичный вызов API с указанием сдвига относительно начала
     * списка папок = 1</li>
     * <li>Проверить, что вернулась ошибка о том, что поиск не возможно использовать вместе с указанием сдвига</li>
     * </ol>
     */
    @Test
    public void testPossibleValuesWithOffsetMoreThenZeroWhenTreePresentation()
    {
        List<Object> result = DSLAttributesApi.listPossibleValues(caseListTreeAttr, userBo, withParams().offset(1));
        DSLAttributesApi.assertPossibleValues(List.of(
                metaClassValue(userCase2),
                metaClassValue(userCase3).leaf(false)
        ), result);

        DSLAttributesApi.assertPossibleValuesError(
                "All possible objects are returned for the search on the first request",
                caseListTreeAttr, userBo, withParams().offset(1).searchString(searchPrefix)
        );
    }

    /**
     * Тестирование получения через API-метод <code>api.attrs.listPossibleValues<code/> возможных значений атрибута
     * типа "Набор типов класса" с древовидным представлением, с указанием и максимального числа элементов
     * для получения и сдвига относительно начала списка найденных элементов.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$283489816
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Получить список возможных значений атрибута <code>caseListAttr</code> объекта <code>userBo</code> через
     * скриптовое API <code>api.attrs.listPossibleValues</code>, с указанием и максимального числа получаемых
     * элементов = 1 и сдвига относительно начала списка найденных элементов = 1</li>
     * <li>Проверить, что вернулся тип <code>userCase2</code>, доступный для выбора и не имеющих дочерних
     * элементов</li>
     * </ol>
     */
    @Test
    public void testPossibleValuesWithOffsetMoreThenZeroAndLimit()
    {
        List<Object> result = DSLAttributesApi.listPossibleValues(caseListAttr, userBo,
                withParams().offset(1).limit(1)
        );
        DSLAttributesApi.assertPossibleValues(List.of(
                metaClassValue(userCase2)
        ), result);
    }

    /**
     * Тестирование получения через API-метод <code>api.attrs.listPossibleValues<code/> возможных значений атрибута с
     * типа "Набор типов класса" с древовидным представлением, с указанием и максимального числа элементов для
     * получения и сдвига относительно начала списка найденных элементов.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$283489816
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Получить список возможных значений атрибута <code>caseListTreeAttr</code> объекта <code>userBo</code>
     * через скриптовое API <code>api.attrs.listPossibleValues</code>, с указанием и максимального числа получаемых
     * элементов = 1, и сдвига относительно начала найденных элементов справочника catalog = 1</li>
     * <li>Проверить, что вернулся тип <code>userCase2</code>, доступный для выбора и не имеющих дочерних элементов</li>
     * </ol>
     */
    @Test
    public void testPossibleValuesWithOffsetMoreThenZeroAndLimitWhenTreePresentation()
    {
        List<Object> result = DSLAttributesApi.listPossibleValues(caseListTreeAttr, userBo,
                withParams().offset(1).limit(1)
        );
        DSLAttributesApi.assertPossibleValues(List.of(
                metaClassValue(userCase2)
        ), result);
    }
}
