package ru.naumen.selenium.cases.operator.permission;

import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.AdminProfileAccessMarker.ADMINISTRATOR_INTERFACE;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.AdminProfileAccessMarker.OPERATOR_INTERFACE;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.PermissionType.ALL;

import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.admin.DSLSuperUser;
import ru.naumen.selenium.casesutil.adminprofiles.DSLAdminProfile;
import ru.naumen.selenium.casesutil.bo.GUISearch;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfile;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix;
import ru.naumen.selenium.casesutil.model.adminprofiles.DAOAdminProfile;
import ru.naumen.selenium.casesutil.model.superuser.DAOSuperUser;
import ru.naumen.selenium.casesutil.model.superuser.SuperUser;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;

/**
 * Тестирование прав на действие в интерфейсе администратора по маркерам доступа в профиле администрирования.
 *
 * <AUTHOR>
 * @since 06.12.2024
 */
public class AdminProfileAccessMarkerTest extends AbstractTestCase
{
    private SuperUser superUser;
    private AdminProfile adminProfile;
    private AdminProfileAccessMarkerMatrix accessMarkerMatrix;

    /**
     * <ol>
     * <b>Общая подготовка для всех тестов</b>
     * <li>Включить доступность профилей администрирования на стенде (customAdminProfiles - true)</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareAllFixture()
    {
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
    }

    /**
     * <ol>
     * <b>Общая подготовка</b>
     * <li>Добавить в систему профиль администрирования adminProfile</li>
     * <li>Создать суперпользователя superUser и назначить ему профиль администрирования adminProfile</li>
     * <li>Создать модель матрицы маркеров доступа accessMarkerMatrix
     * и добавить в нее право на доступ к маркеру доступа "Интерфейс администратора"</li>
     * <li>Установить в профиль администрирования adminProfile матрицу маркеров доступа accessMarkerMatrix</li>
     * </ol>
     */
    @Before
    public void prepareFixture()
    {
        adminProfile = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(adminProfile);

        superUser = DAOSuperUser.create();
        superUser.setAdminProfiles(adminProfile);
        DSLSuperUser.add(superUser);

        accessMarkerMatrix = new AdminProfileAccessMarkerMatrix();
        accessMarkerMatrix.addAdministrationInterfacePermission();
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
    }

    /**
     * Тестирование действия "Доступ" маркера доступа "Интерфейс оператора". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в интерфейс оператора</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что присутствует кнопка смены интерфейса</li>
     * <li>Проверить, что присутствует кнопка "Выйти"</li>
     * <li>Проверить, что отсутствует панель поиска</li>
     * <li>Добавить в матрицу маркеров доступа accessMarkerMatrix право на доступ к маркеру доступа
     * "Интерфейс оператора" и убрать право на доступ к маркеру доступа "Интерфейс администратора"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что сообщение об ошибке отсутствует</li>
     * <li>Проверить, что отсутствует кнопка смены интерфейса</li>
     * <li>Проверить, что присутствует кнопка "Выйти"</li>
     * <li>Проверить, что присутствует панель поиска</li>
     * </ol>
     */
    @Test
    public void testAllPermissionOperatorInterfaceAccessMarker()
    {
        GUILogon.login(superUser);
        GUINavigational.goToOperator();
        GUIError.assertDialogError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUINavigational.assertSwitchInterfacePresent();
        GUILogon.assertLogoutButtonPresent();
        GUISearch.assertAbsentSearchPanel();

        accessMarkerMatrix.addAccessMarkerPermission(OPERATOR_INTERFACE, ALL);
        accessMarkerMatrix.removeAccessMarkerPermission(ADMINISTRATOR_INTERFACE, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        tester.refresh();
        GUIError.assertErrorAbsence();
        GUINavigational.assertSwitchInterfaceAbsence();
        GUILogon.assertLogoutButtonPresent();
        GUISearch.assertPresentSearchPanel();
    }
}