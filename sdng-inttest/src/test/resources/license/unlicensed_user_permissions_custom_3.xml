<?xml version="1.0" encoding="UTF-8" standalone="no"?><ns2:naumen-license xmlns:ns2="http://naumen.ru/license">
    <baseLevel>server</baseLevel>
    <client>Naumen (Dev-стенд)</client>
    <author><PERSON><PERSON><PERSON><PERSON></author>
    <creationDate>2025.03.04 15:00</creationDate>
    <parameter name="rolesForUnlicensedUsers">
        employee, ouHead, currentUser, commentAuthor, fileAuthor, ServiceCallClient, ouMember, AgreementRecipient, slmServiceRecipient, serviceCallEmployeeOfClientOU
    </parameter>
	<parameter expirationDate="2025.01.20" name="permissionsSetForUnlicensedUsers">
        custom
    </parameter>	
	<parameter expirationDate="2024.09.24" name="transitionsByStateToUnlicensedUser">
       userClass: *FINAL_STATE*;		   
	   userClass$userCase: *FINAL_STATE*;	   
    </parameter>
    <parameter name="isTitleLocalizationAllowed">true</parameter>
    <parameter name="restSessionsControlDisabled">true</parameter>
    <parameter name="accessToSMPInterface">true</parameter>
    <superuser code="superuser" count="10" expirationDate="2025.01.20"/>
    <named accessToSMPInterface="true" code="named" count="10" expirationDate="2025.01.20"/>
    <concurrent accessToSMPInterface="true" code="concurrent" count="10" expirationDate="2025.01.20"/>
    <quantitativeLicense>
        <quota code="quota" delay="true" delayLimit="0" max="10" warn="80">
            <limitedClassesOrTypesWithHeirs>quotaClass</limitedClassesOrTypesWithHeirs>
            <exceptionsOfLimitedClassesOrTypesWithHeirs>quotaClass$exceptedType</exceptionsOfLimitedClassesOrTypesWithHeirs>
        </quota>
    </quantitativeLicense>
    <modules expirationDate="2025.01.20">cti, cmdb, admin-lite, workload, mobile-api, ndap, smia, plannedVersion, omnichannel, portal, dynamicField</modules>
</ns2:naumen-license>