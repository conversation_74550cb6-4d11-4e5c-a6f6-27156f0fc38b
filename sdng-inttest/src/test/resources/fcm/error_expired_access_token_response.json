{"error": {"code": 401, "message": "Request had invalid authentication credentials. Expected OAuth 2 access token, login cookie or other valid authentication credential. See https://developers.google.com/identity/sign-in/web/devconsole-project.", "status": "UNAUTHENTICATED", "details": [{"@type": "type.googleapis.com/google.rpc.ErrorInfo", "reason": "ACCESS_TOKEN_EXPIRED", "domain": "googleapis.com", "metadata": {"method": "google.firebase.fcm.v1.FcmService.SendMessage", "service": "fcm.googleapis.com"}}]}}