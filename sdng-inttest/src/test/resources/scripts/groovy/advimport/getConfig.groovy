[BEGIN IMPORT]
ru.naumen.advimport.shared.dispatch.GetConfigurationAction;
[END IMPORT]
[BEGIN DECLARATION]
getConfig('%s')
[END DECLARATION]
[BEGIN BODY]
/**
 * Получить конфигураций импорта
 * @return result подключение в json формате.
 */
def getConfig(def uuid)
{
    def conf = dispatch.execute(new GetConfigurationAction('advimport$' + uuid)).get().get()
    def result = [:]
    result.put('config', conf.configContainer.config)
  
    def title = conf.title.find{ it -> it.lang.equals("ru") }

    result.put('title', title.value)
    result.put('uuid', conf.uuid)
    return GSON.SELF.toJson(result)
}
[END BODY]