[BEGIN IMPORT]
ru.naumen.advimport.shared.dispatch.AddConfigurationAction
[END IMPORT]
[BEGIN DECLARATION]
addConfig('%s','%s', %s, '%s')
[END DECLARATION]
[BEGIN BODY]
/**
 * Добавить конфигурацию импорта.
 * @param uuid код конфигурации импорта (Обязательный параметр).
 * @param title название конфигурации импорта (Обязательный параметр).
 * @param config конфигурация импорта (Обязательный параметр).
 * @return uuid конфигурации импорта.
 */
def addConfig(def uuid, def title, def config, def settingsSet)
{
	def settingsSetCode = !settingsSet || 'null' == settingsSet ? null : settingsSet;
	def result = dispatch.execute(new AddConfigurationAction(uuid, title, config, settingsSetCode));
	return result.get().get().getUUID();
}
[END BODY]