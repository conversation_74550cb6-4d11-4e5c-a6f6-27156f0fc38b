<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<metainfoContainer>
    <head>
        <date>2024-04-07T22:46:18.047+03:00</date>
        <exportMode>partial</exportMode>
        <version>4.18.5-SNAPSHOT</version>
    </head>
    <tags/>
    <sets/>
    <user-metaclass seg-detach="true" seg-id="testUserClass" seg-type="metaclasses">
        <fqn>
            <id>testUserClass</id>
        </fqn>
        <parent>
            <id>userEntity</id>
        </parent>
        <title lang="ru">userClass</title>
        <description lang="ru"/>
        <properties/>
        <tags/>
        <responsibilityTransferTableEnabled>true</responsibilityTransferTableEnabled>
        <attributes>
            <attribute seg-detach="true" seg-id="source" seg-type="attribute">
                <code>source</code>
                <hiddenAttrCaption>false</hiddenAttrCaption>
                <computable>false</computable>
                <editable>true</editable>
                <editableInLists>false</editableInLists>
                <required>false</required>
                <requiredInInterface>false</requiredInInterface>
                <unique>false</unique>
                <determinable>false</determinable>
                <title lang="ru">testSboAttr</title>
                <description lang="ru"/>
                <type>
                    <code>object</code>
                    <property code="code">object</property>
                    <property code="complexRelation">false</property>
                    <property code="metaClassFqn">testUserClass</property>
                    <property code="permittedTypes">["testUserClass"]</property>
                </type>
                <filteredByScript>false</filteredByScript>
                <computableOnForm>false</computableOnForm>
                <viewPresentation>
                    <code>boRreference</code>
                </viewPresentation>
                <editPresentation>
                    <code>boSelect</code>
                </editPresentation>
                <useGenerationRule>false</useGenerationRule>
                <accessor>flexAccessor</accessor>
                <defaultByScript>false</defaultByScript>
                <systemEditable>true</systemEditable>
                <searchSetting seg-detach="true" seg-id="source_source" seg-type="search-settings">
                    <simpleSearchableForLicensed>false</simpleSearchableForLicensed>
                    <simpleSearchableForNotLicensed>false</simpleSearchableForNotLicensed>
                    <extendedSearchableForNotLicensed>false</extendedSearchableForNotLicensed>
                    <extendedSearchableForLicensed>false</extendedSearchableForLicensed>
                    <searchBoost>1.0</searchBoost>
                    <code>source</code>
                    <declaredMetaClass>testUserClass</declaredMetaClass>
                    <attrCode>source</attrCode>
                </searchSetting>
                <exportNDAP>false</exportNDAP>
                <hiddenWhenEmpty>false</hiddenWhenEmpty>
                <hiddenWhenNoPossibleValues>false</hiddenWhenNoPossibleValues>
                <advlistSemanticFiltering>false</advlistSemanticFiltering>
                <editOnComplexFormOnly>false</editOnComplexFormOnly>
                <hideArchived>true</hideArchived>
            </attribute>
        </attributes>
        <attribute-overrides/>
        <permittedTypesInfo/>
        <attribute-groups/>
        <workflow seg-detach="true" seg-id="workflow" seg-type="workflow"/>
        <maxSearchResults>3</maxSearchResults>
        <searchOrder>9</searchOrder>
        <status>DEFAULT</status>
    </user-metaclass>
    <mail-processor-rules/>
    <style-templates/>
    <list-templates/>
    <content-templates/>
    <user-events/>
    <event-actions/>
    <system-jmsqueues/>
    <user-jmsqueues/>
    <embedded-applications/>
    <custom-forms/>
    <advimport/>
    <script-modules/>
    <scripts/>
    <customJSElements/>
    <fast-link-settings/>
    <objects/>
    <transfer-values/>
    <structured-objects-views/>
    <libraries/>
    <eventStorageRules/>
</metainfoContainer>
