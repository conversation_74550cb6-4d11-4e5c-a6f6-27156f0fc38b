<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<metainfoContainer>
    <head>
        <date>2024-04-04T16:50:12.396+03:00</date>
        <exportMode>partial</exportMode>
        <version>4.18.5-SNAPSHOT</version>
    </head>
    <tags/>
    <sets/>
    <user-metaclass seg-detach="true" seg-id="ou$testOu" seg-type="metaclasses">
        <fqn>
            <id>ou</id>
            <case>testOu</case>
        </fqn>
        <parent>
            <id>ou</id>
        </parent>
        <title lang="ru">testOu</title>
        <description lang="ru"/>
        <properties/>
        <tags/>
        <responsibilityTransferTableEnabled>true</responsibilityTransferTableEnabled>
        <attributes>
            <attribute seg-detach="true" seg-id="content" seg-type="attribute">
                <code>content</code>
                <hiddenAttrCaption>false</hiddenAttrCaption>
                <computable>false</computable>
                <editable>true</editable>
                <editableInLists>false</editableInLists>
                <required>false</required>
                <requiredInInterface>false</requiredInInterface>
                <unique>false</unique>
                <determinable>false</determinable>
                <title lang="ru">testAttrString</title>
                <description lang="ru"/>
                <type>
                    <code>string</code>
                    <property code="code">string</property>
                    <property code="inputMask"/>
                    <property code="inputMaskMode"/>
                    <property code="string">255</property>
                </type>
                <filteredByScript>false</filteredByScript>
                <computableOnForm>false</computableOnForm>
                <viewPresentation>
                    <code>stringView</code>
                </viewPresentation>
                <editPresentation>
                    <code>stringEdit</code>
                </editPresentation>
                <useGenerationRule>false</useGenerationRule>
                <accessor>flexAccessor</accessor>
                <defaultValue>
                    <string>Нередактированное значение</string>
                </defaultValue>
                <defaultByScript>false</defaultByScript>
                <systemEditable>true</systemEditable>
                <searchSetting seg-detach="true" seg-id="content_content" seg-type="search-settings">
                    <simpleSearchableForLicensed>false</simpleSearchableForLicensed>
                    <simpleSearchableForNotLicensed>false</simpleSearchableForNotLicensed>
                    <extendedSearchableForNotLicensed>false</extendedSearchableForNotLicensed>
                    <extendedSearchableForLicensed>false</extendedSearchableForLicensed>
                    <searchBoost>1.0</searchBoost>
                    <code>content</code>
                    <declaredMetaClass>ou$testOu</declaredMetaClass>
                    <attrCode>content</attrCode>
                </searchSetting>
                <exportNDAP>false</exportNDAP>
                <hiddenWhenEmpty>false</hiddenWhenEmpty>
                <hiddenWhenNoPossibleValues>false</hiddenWhenNoPossibleValues>
                <advlistSemanticFiltering>false</advlistSemanticFiltering>
                <editOnComplexFormOnly>false</editOnComplexFormOnly>
                <hideArchived>true</hideArchived>
            </attribute>
        </attributes>
        <attribute-overrides/>
        <permittedTypesInfo/>
        <attribute-groups/>
        <workflow seg-detach="true" seg-id="workflow" seg-type="workflow"/>
        <maxSearchResults>3</maxSearchResults>
        <searchOrder>1</searchOrder>
        <status>DEFAULT</status>
    </user-metaclass>
    <mail-processor-rules/>
    <style-templates/>
    <list-templates/>
    <content-templates/>
    <user-events/>
    <event-actions/>
    <system-jmsqueues/>
    <user-jmsqueues/>
    <embedded-applications/>
    <custom-forms/>
    <advimport/>
    <script-modules/>
    <scripts/>
    <customJSElements/>
    <fast-link-settings/>
    <objects/>
    <transfer-values/>
    <structured-objects-views/>
    <libraries/>
    <eventStorageRules/>
</metainfoContainer>
