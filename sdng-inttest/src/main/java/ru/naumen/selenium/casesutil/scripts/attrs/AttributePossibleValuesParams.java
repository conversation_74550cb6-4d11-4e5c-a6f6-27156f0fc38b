package ru.naumen.selenium.casesutil.scripts.attrs;

import java.util.HashMap;
import java.util.Map;

import ru.naumen.selenium.casesutil.model.ModelUuid;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;

/**
 * Параметры получения возможных значений
 *
 * <AUTHOR>
 * @since 06.11.2024
 */
public class AttributePossibleValuesParams
{
    /**
     * Задать параметры получения возможных значений
     */
    public static AttributePossibleValuesParams withParams()
    {
        return new AttributePossibleValuesParams();
    }

    /** Родитель, для получения дочерних объектов */
    private String parent;
    /** Максимальное число элементов, которое надо вернуть */
    private Integer limit;
    /** Сдвиг относительно начала списка всех найденных объектов **/
    private Integer offset;
    /** Сдвиг относительно начала списка для каждого из классов */
    private final Map<String, Integer> typeOffsets = new HashMap<>();
    /** Текст поискового запроса */
    private String searchString;

    private AttributePossibleValuesParams()
    {
    }

    /**
     * Устанавливает родителя, для получения дочерних объектов
     *
     * @param parent бизнес-объект, элемент справочника или папка
     */
    public AttributePossibleValuesParams parent(ModelUuid parent)
    {
        this.parent = parent.getUuid();
        return this;
    }

    /**
     * Устанавливает родителя, для получения дочерних объектов
     *
     * @param parent метакласс
     */
    public AttributePossibleValuesParams parent(MetaClass parent)
    {
        this.parent = parent.getFqn();
        return this;
    }

    /**
     * Устанавливает максимальное число элементов, которое надо вернуть.
     *
     * @param limit максимальное число элементов, которое надо вернуть
     */
    public AttributePossibleValuesParams limit(int limit)
    {
        this.limit = limit;
        return this;
    }

    /**
     * Устанавливает сдвиг относительно начала списка всех найденных объектов.
     * Применим для списочных представлений без папок (включая ЭС/НЭС в представлении списком).
     *
     * @param offset значение сдвига
     */
    public AttributePossibleValuesParams offset(int offset)
    {
        this.offset = offset;
        return this;
    }

    /**
     * Устанавливает сдвиг относительно начала списка для метакласса
     *
     * @param metaClass метакласс
     * @param offset значение сдвига
     */
    public AttributePossibleValuesParams addTypeOffset(MetaClass metaClass, int offset)
    {
        this.typeOffsets.put(metaClass.getFqn(), offset);
        return this;
    }

    /**
     * Устанавливает сдвиг относительно начала списка для ЭС/НЭС с представлением в виде дерева
     *
     * @param catalog каталог
     * @param offset значение сдвига
     */
    public AttributePossibleValuesParams addTypeOffset(Catalog catalog, int offset)
    {
        this.typeOffsets.put(catalog.getCode(), offset);
        return this;
    }

    /**
     * Устанавливает сдвиг относительно начала списка для папок
     *
     * @param offset значение сдвига
     */
    public AttributePossibleValuesParams addFolderOffset(int offset)
    {
        this.typeOffsets.put(CatalogItem.IS_FOLDER, offset);
        return this;
    }

    /**
     * Устанавливает текст поискового запроса
     *
     * @param searchString текст поискового запроса
     */
    public AttributePossibleValuesParams searchString(String searchString)
    {
        this.searchString = searchString;
        return this;
    }

    String getParent()
    {
        return parent;
    }

    Integer getLimit()
    {
        return limit;
    }

    Integer getOffset()
    {
        return offset;
    }

    String getSearchString()
    {
        return searchString;
    }

    Map<String, Integer> getTypeOffsets()
    {
        return (!typeOffsets.isEmpty()) ? typeOffsets : null;
    }
}
