package ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators;

import static org.hamcrest.CoreMatchers.is;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;

/**
 * Валидатор для возможных значений атрибута, представленных в виде списка, без учёта порядка элементов.
 *
 * <AUTHOR>
 * @since 29.03.2023
 */
public class ListPossibleValuesInAnyOrderValidator extends
        BasePossibleValuesValidator<ListPossibleValueValidator, ListPossibleValuesInAnyOrderValidator>
{
    private boolean ignoreCount = false;

    ListPossibleValuesInAnyOrderValidator()
    {
        super("");
    }

    /**
     * Устанавливает флаг необходимости проверки количества полученных значений. Ожидаемое количество равно числу
     * установленных валидаторов
     */
    public ListPossibleValuesInAnyOrderValidator ignoreCount(boolean ignoreCount)
    {
        this.ignoreCount = ignoreCount;
        return this;
    }

    @Override
    public void validate(ValidatableResponse response, String absolutePath, MobileVersion version)
    {
        for (ListPossibleValueValidator validator : validators)
        {
            String path = absolutePath + "?.find { it.code == '%s' }".formatted(validator.getUuid());
            validator.validate(response, path, version);
        }

        if (!ignoreCount)
        {
            response.body(absolutePath + "?.size()", is(validators.size()));
        }
    }
}
