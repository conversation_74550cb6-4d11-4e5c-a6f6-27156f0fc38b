package ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators;

/**
 * Валидатор для возможных значений атрибута, представленных в виде списка.
 *
 * <AUTHOR>
 * @since 23.03.2022
 */
public class ListPossibleValuesValidator
        extends BasePossibleValuesValidator<ListPossibleValueValidator, ListPossibleValuesValidator>
{
    ListPossibleValuesValidator()
    {
        super("");
    }
}
