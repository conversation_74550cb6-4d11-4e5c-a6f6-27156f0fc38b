package ru.naumen.selenium.casesutil.mobile.rest.lists.validators;

import static org.hamcrest.CoreMatchers.is;

import java.util.List;

import io.restassured.response.ValidatableResponse;

/**
 * Базовая реализация валидатора для списочного действия, выполняющая валидацию общих параметров действий.
 *
 * <AUTHOR>
 * @since 22.11.2024
 */
public class AbstractListActionValidator<T extends AbstractListActionValidator<?>> implements ListActionValidator
{
    public static final String SYSTEM = "system";

    private final String code;
    private final String title;

    AbstractListActionValidator(String code, String title)
    {
        this.code = code;
        this.title = title;
    }

    @Override
    public void validate(ValidatableResponse response, String absolutePath)
    {
        response.body(absolutePath + "?.type", is(SYSTEM));
        response.body(absolutePath + "?.code", is(code));
        response.body(absolutePath + "?.title", is(title));
        response.body(absolutePath + "?.requiredFields", is(List.of()));
    }
}
