package ru.naumen.selenium.casesutil.model.bo;

import java.util.Arrays;
import java.util.stream.Collectors;

import ru.naumen.selenium.casesutil.model.ModelFactory;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.dynamicfield.DynamicFieldConfiguration;
import ru.naumen.selenium.casesutil.model.metaclass.DAODynamicFieldCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.Json;
import ru.naumen.selenium.util.StringUtils;

/**
 * Методы для создания моделей динамических полей.
 * <AUTHOR>
 * @since Sep 01, 2022
 */
public class DAODynamicField
{
    /**
     * Создает модель шаблона динамического атрибута.
     * (Модель регистрируется в очередь на удаление.)
     * @param fieldType тип поля
     * @param viewPresentation представление для отображения
     * @param editPresentation представление для редактирования
     * @param objectTypes типы объектов для ссылочных полей
     * @return модель динамического атрибута
     */
    public static Bo create(String fieldType, String viewPresentation, String editPresentation,
            MetaClass... objectTypes)
    {
        MetaClass fieldCase = DAODynamicFieldCase.createCase();
        Bo field = ModelFactory.create(Bo.class);
        field.setBoType(SystemClass.DYNAMIC_FIELD.getCode());
        field.setMetaclassTitle(fieldCase.getTitle());
        field.setMetaclassCode(fieldCase.getCode());
        field.setMetaclassFqn(fieldCase.getFqn());
        field.setTitle(SystemClass.DYNAMIC_FIELD.getTitleModel() + ModelUtils.createTitle());

        field.setFieldOrder(0L);
        field.setFieldType(SharedFixture.dynamicFieldType(fieldType).getUuid());
        field.setFieldTypeCode(fieldType);
        field.setFieldViewPresentation(SharedFixture.dynamicFieldPresentation(viewPresentation).getUuid());
        field.setFieldViewPresentationCode(viewPresentation);
        field.setFieldEditPresentation(SharedFixture.dynamicFieldPresentation(editPresentation).getUuid());
        field.setFieldEditPresentationCode(editPresentation);
        field.setFieldObjects(Json.listToString(
                Arrays.stream(objectTypes).map(MetaClass::getFqn).collect(Collectors.toList())));
        return field;
    }

    /**
     * Создает модель атрибута динамического поля.
     * @param dynAttribute динамический атрибут
     * @param dynamicField шаблон динамического поля
     * @return модель атрибута
     */
    public static Attribute createDynamicAttribute(Attribute dynAttribute, Bo dynamicField)
    {
        return createDynamicAttribute(dynAttribute, dynamicField, dynamicField.getFieldTypeCode(),
                dynamicField.getFieldViewPresentationCode(), dynamicField.getFieldEditPresentationCode());
    }

    /**
     * Создает модель атрибута динамического поля.
     * @param dynAttribute динамический атрибут
     * @param dynamicField шаблон динамического поля
     * @param fieldType тип поля
     * @param viewPresentation представление для отображения
     * @param editPresentation представление для редактирования
     * @return модель атрибута
     */
    public static Attribute createDynamicAttribute(Attribute dynAttribute, Bo dynamicField,
            String fieldType, String viewPresentation, String editPresentation)
    {
        String id = StringUtils.substringAfter(dynamicField.getUuid(), "$");
        Attribute attribute = DAOAttribute.createPseudo(dynamicField.getTitle(), dynAttribute.getCode() + '/' + id);
        attribute.setFqn(dynAttribute.getFqn() + '/' + id);
        attribute.setParentFqn(dynAttribute.getParentFqn());
        attribute.setType(fieldType);
        attribute.setViewPresentation(viewPresentation);
        attribute.setEditPresentation(editPresentation);
        return attribute;
    }

    /**
     * Создает объект конфигурации динамических полей.
     * @return новая конфигурация динамических полей
     */
    public static DynamicFieldConfiguration createConfiguration()
    {
        return ModelFactory.create(DynamicFieldConfiguration.class);
    }
}
