package ru.naumen.selenium.casesutil.admin.library;

import static ru.naumen.selenium.core.WaitTool.WAIT_TIME_LIBRARIES;

import java.nio.file.Paths;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.file.GUIFileAdmin;
import ru.naumen.selenium.core.Cleaner;

/**
 * GUI методы для работы с библиотеками
 * <AUTHOR>
 * @since 28.04.2020
 */
public class GUILibrary
{
    private static final String LIBRARY_UPLOAD = "//*[@id='gwt-debug-librariesUpload']//form";

    /**
     * Загрузить библиотеку.
     * При загрузке проверяется наличия сообщения "Библиотека $libraryName была успешно загружена"
     * @param library путь до библиотеки
     * @see DSLLibrary
     */
    public static void upload(String library)
    {
        // TODO NSDPRD-32728 Удалить метод
        DSLLibrary.addScriptFix();

        final String libraryName = Paths.get(library).getFileName().toString();
        GUIFileAdmin.uploadFileWithoutValidation(LIBRARY_UPLOAD, library);
        GUIForm.assertInfoDialog(String.format("Библиотека '%s' была успешно загружена", libraryName),
                WAIT_TIME_LIBRARIES);
        GUIForm.applyInfoDialog();
        Cleaner.afterTest(() -> DSLLibrary.deleteLibrary(libraryName));
    }

    /**
     * Загрузить библиотеку и проверить полученное сообщение об ошибке
     * @param library путь до библиотеки
     * @param errorMessage ожидаемое сообщение об ошибке
     */
    public static void uploadAndAssertError(String library, String errorMessage)
    {
        // TODO NSDPRD-32728 Удалить метод
        DSLLibrary.addScriptFix();

        GUIFileAdmin.uploadFileAssertError(LIBRARY_UPLOAD, library, errorMessage);
    }
}
