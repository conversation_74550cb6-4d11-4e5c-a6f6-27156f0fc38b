package ru.naumen.selenium.casesutil.mobile.contents.listcontent;

/**
 * Колонки списка действий в контентах "Список связанных объектов" и "Список вложенных объектов"
 *
 * <AUTHOR>
 * @since 08.05.2024
 */
public enum MobileListContentActionColumns
{
    /** Колонка "Название" */
    TITLE(3),
    /** Колонка "Действие" */
    TYPE(4),
    /** Колонка "Передавать геопозицию устройства" */
    REQUIRED_GEOLOCATION(5),
    /** Колонка редактирования действия */
    EDIT(6),
    /** Колонка удаления действия */
    DELETE(7);

    private final int column;

    MobileListContentActionColumns(int column)
    {
        this.column = column;
    }

    /**
     * Возвращает номер колонки в таблице, представляющей список действий в контенте
     */
    public int getColumn()
    {
        return column;
    }
}
