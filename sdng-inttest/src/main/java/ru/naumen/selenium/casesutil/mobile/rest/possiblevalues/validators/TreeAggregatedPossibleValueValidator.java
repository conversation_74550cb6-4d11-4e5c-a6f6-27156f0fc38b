package ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators;

import static org.hamcrest.CoreMatchers.is;
import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.FROM_V15;

import java.util.Arrays;

import io.restassured.response.ValidatableResponse;
import jakarta.annotation.Nullable;
import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;
import ru.naumen.selenium.casesutil.model.ModelUuid;

/**
 * Валидатор для возможного значения агрегируемого атрибута в дереве.
 *
 * <AUTHOR>
 * @since 01.08.2024
 */
public class TreeAggregatedPossibleValueValidator
        extends BaseTreePossibleValueValidator<TreeAggregatedPossibleValueValidator>
{
    private final Object uuid;
    private TreeAggregatedObjectValueValidator[] valueValidators = new TreeAggregatedObjectValueValidator[0];

    TreeAggregatedPossibleValueValidator(@Nullable String uuid, @Nullable String title)
    {
        super(title);

        this.uuid = uuid;
    }

    /**
     * Позволяет валидировать цепочку уникальных идентификаторов (UUID), однозначно определяющих значение
     * @param value цепочка уникальных идентификаторов (UUID)
     */
    public TreeAggregatedPossibleValueValidator value(ModelUuid... value)
    {
        this.valueValidators = Arrays.stream(value)
                .map(TreeAggregatedObjectValueValidator::new)
                .toArray(TreeAggregatedObjectValueValidator[]::new);
        return this;
    }

    /**
     * Позволяет валидировать цепочку уникальных идентификаторов (UUID), однозначно определяющих значение
     * @param validators цепочка уникальных идентификаторов (UUID)
     */
    public TreeAggregatedPossibleValueValidator value(TreeAggregatedObjectValueValidator... validators)
    {
        this.valueValidators = validators;
        return this;
    }

    @Override
    public void validate(ValidatableResponse response, String absolutePath, MobileVersion version)
    {
        super.validate(response, absolutePath, version);

        if (FROM_V15.contains(version))
        {
            String path = absolutePath + "?.value";
            for (int i = 0; i < valueValidators.length; i++)
            {
                valueValidators[i].validate(response, path + "[" + i + "]");
            }

            response.body(path + "?.size()", is(valueValidators.length));
        }
        else
        {
            response.body(absolutePath + "?.uuid", is(uuid));
        }
    }
}
