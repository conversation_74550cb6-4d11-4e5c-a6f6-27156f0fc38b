package ru.naumen.selenium.casesutil.admin.interfaze;

import java.io.File;
import java.util.List;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;

/**
 * Класс для тестирования настроек логотипов UI 2 через интерфейс
 *
 * <AUTHOR>
 * @since 05.09.24
 */
public class GUILogoUi2 extends CoreTester
{
    //@formatter:off
    private static final String UI2_DIR = GUIInterface.FOLDER_UPLOAD_FILES_PATH + "ui2Logo" + File.separator;
    public static final String USER_LOGO_1 =  UI2_DIR + "userLogo1.svg";
    public static final String USER_LOGO_2 =  UI2_DIR + "userLogo2.svg";
    public static final String X_LIGHT_LOGO_INPUT = "//*[@id='gwt-debug-lightLogoValue']//input";
    public static final String X_LIGHT_LOGO_FILE_UPLOAD = "//*[@id='gwt-debug-lightLogoValueFileUpload']";
    public static final String X_MINI_LIGHT_LOGO_INPUT = "//*[@id='gwt-debug-miniLightLogoValue']//input";
    public static final String X_MINI_LIGHT_LOGO_FILE_UPLOAD = "//*[@id='gwt-debug-miniLightLogoValueFileUpload']";
    public static final String X_DARK_LOGO_INPUT = "//*[@id='gwt-debug-darkLogoValue']//input";
    public static final String X_DARK_LOGO_FILE_UPLOAD = "//*[@id='gwt-debug-darkLogoValueFileUpload']";
    public static final String X_MINI_DARK_LOGO_INPUT = "//*[@id='gwt-debug-miniDarkLogoValue']//input";
    public static final String X_MINI_DARK_LOGO_FILE_UPLOAD = "//*[@id='gwt-debug-miniDarkLogoValueFileUpload']";
    public static final String LIST_FROM_FILE_VALUE = "Из файла";
    public static final String LIST_LIGHT_LOGO_VALUE = "Логотип для светлого режима";
    public static final String LIST_STANDARD_ID = "STANDARD";
    public static final String LIST_FROM_FILE_ID = "FILE";
    public static final String LIST_LIGHT_LOGO_ID = "LIGHT";

    private static final String X_UI2_LOGO_TABLE = "//*[@id='gwt-debug-ui2LogoTable']";
    private static final String X_UI2_LOGO_EDIT = "//*[@id='gwt-debug-ui2Logo']//*[@id='gwt-debug-edit']";
    private static final String X_UI2_LOGO_IMG = "/div/div/img";
    private static final String X_LIGHT_LOGO = X_UI2_LOGO_TABLE + "/tbody[1]/tr[1]/td[2]" + X_UI2_LOGO_IMG;
    private static final String X_MINI_LIGHT_LOGO = X_UI2_LOGO_TABLE + "/tbody[1]/tr[2]/td[2]" + X_UI2_LOGO_IMG;
    private static final String X_DARK_LOGO = X_UI2_LOGO_TABLE + "/tbody[1]/tr[3]/td[2]" + X_UI2_LOGO_IMG;
    private static final String X_MINI_DARK_LOGO =X_UI2_LOGO_TABLE + "/tbody[1]/tr[4]/td[2]" + X_UI2_LOGO_IMG;
    private static final String X_DARK_LOGO_CONTAINER = X_UI2_LOGO_TABLE + "/tbody[1]/tr[3]/td[2]"  + X_UI2_LOGO_IMG;
    private static final String X_MINI_DARK_LOGO_CONTAINER =X_UI2_LOGO_TABLE + "/tbody[1]/tr[4]/td[2]"  + X_UI2_LOGO_IMG;
    private static final String LIST_STANDARD_LIGHT_VALUE = "SMP";
    private static final String LIST_STANDARD_DARK_VALUE = "SMP для темного режима";
    private static final List<String> LIST_LIGHT_VALUES = List.of(LIST_STANDARD_LIGHT_VALUE, LIST_FROM_FILE_VALUE);
    private static final List<String> LIST_DARK_VALUES = List.of(LIST_STANDARD_DARK_VALUE, LIST_LIGHT_LOGO_VALUE, LIST_FROM_FILE_VALUE);
    //@formatter:on

    /**
     * Проверить доступные варианты для выбора в логотипах:
     * для логотипов светлого режима: SMP, из файла
     * для логотипов темного режима: SMP для темного режима, Логотип для светлого режима, из файла
     */
    public static void assertAvailableValues()
    {
        GUISelect.assertSelect(X_LIGHT_LOGO_INPUT, LIST_LIGHT_VALUES, true, true, true);
        GUISelect.assertSelect(X_MINI_LIGHT_LOGO_INPUT, LIST_LIGHT_VALUES, true, true, true);
        GUISelect.assertSelect(X_DARK_LOGO_INPUT, LIST_DARK_VALUES, true, true, true);
        GUISelect.assertSelect(X_MINI_DARK_LOGO_INPUT, LIST_DARK_VALUES, true, true, true);
    }

    /**
     * Проверить выбранное значение для логотипа
     * @param xpathLogo - путь до списка выбора значения
     * @param name - ожидаемое значение
     */
    public static void assertLogoValue(String xpathLogo, String name)
    {
        GUISelect.assertSelected(xpathLogo, name);
    }

    /**
     * Проверить, что для всех логотипов выбрано стандартное значение
     */
    public static void assertStandardSelect()
    {
        GUISelect.assertSelected(X_LIGHT_LOGO_INPUT, LIST_STANDARD_LIGHT_VALUE);
        GUISelect.assertSelected(X_MINI_LIGHT_LOGO_INPUT, LIST_STANDARD_LIGHT_VALUE);
        GUISelect.assertSelected(X_DARK_LOGO_INPUT, LIST_STANDARD_DARK_VALUE);
        GUISelect.assertSelected(X_MINI_DARK_LOGO_INPUT, LIST_STANDARD_DARK_VALUE);
    }

    /**
     * Проверить наличие контента с логотипами UI 2
     */
    public static void assertUi2AdminLogoSettingsPresent()
    {
        GUITester.assertExists(X_UI2_LOGO_TABLE, true);
    }

    /**
     * @return цвет фона у логотипа для темного режима
     */
    public static String getDarkContainerColor()
    {
        return tester.getBackgroundColor(X_DARK_LOGO_CONTAINER);
    }

    /**
     * @return html представление логотипа для темного режима
     */
    public static String getDarkLogo()
    {
        return getLogo(X_DARK_LOGO);
    }

    /**
     * @return html представление логотипа для светлого режима
     */
    public static String getLightLogo()
    {
        return getLogo(X_LIGHT_LOGO);
    }

    /**
     * @return цвет фона у мини логотипа для темного режима
     */
    public static String getMiniDarkContainerColor()
    {
        return tester.getBackgroundColor(X_MINI_DARK_LOGO_CONTAINER);
    }

    /**
     * @return html представление мини логотипа для темного режима
     */
    public static String getMiniDarkLogo()
    {
        return getLogo(X_MINI_DARK_LOGO);
    }

    /**
     * @return html представление мини логотипа для светлого режима
     */
    public static String getMiniLightLogo()
    {
        return getLogo(X_MINI_LIGHT_LOGO);
    }

    /**
     * Открыть форму редактирования логотипов UI 2
     */
    public static void openEditUi2LogoForm()
    {
        tester.click(X_UI2_LOGO_EDIT);
    }

    /**
     * Установить значение логотипа на форме редактирования логотипов UI 2
     * @param xpath - путь к списку для выбора значения
     * @param value - значение из списка
     */
    public static void selectLogoValue(String xpath, String value)
    {
        GUISelect.select(xpath, value);
    }

    private static String getLogo(String xpath)
    {
        return tester.getHtml(xpath);
    }
}