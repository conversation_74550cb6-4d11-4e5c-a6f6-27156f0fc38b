package ru.naumen.selenium.casesutil.mobile.contents.listcontent;

import ru.naumen.selenium.casesutil.model.mobile.MobileAddForm;

/**
 * Объект, хранящий данные заполненных действий на формах добаления и редактирования действий в контентах "Список
 * связанных объектов" и "Список вложенных объектов"
 *
 * <AUTHOR>
 * @since 08.05.2024
 */
public class MobileListContentActionHolder
{
    /**
     * Создаёт данные о действии "Сортировка"
     */
    public static MobileListContentActionHolder createSortAction()
    {
        MobileListContentActionHolder holder = new MobileListContentActionHolder();
        holder.setTitle(MobileListContentActionType.SORT.getActionTitle());
        holder.setType(MobileListContentActionType.SORT);
        return holder;
    }

    /** Значение поля "Название" */
    private String title;
    /** Значение поля "Действие" */
    private MobileListContentActionType type;
    /** Значение поля "Форма добавления" */
    private MobileAddForm addForm;
    /** Значение поля "Передавать геопозицию устройства" */
    private boolean isRequiredGeo;

    public String getTitle()
    {
        return title;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public MobileListContentActionType getType()
    {
        return type;
    }

    public void setType(MobileListContentActionType type)
    {
        this.type = type;
    }

    public MobileAddForm getAddForm()
    {
        return addForm;
    }

    public void setAddForm(MobileAddForm addForm)
    {
        this.addForm = addForm;
    }

    public boolean isRequiredGeo()
    {
        return isRequiredGeo;
    }

    public void setRequiredGeo(boolean requiredGeo)
    {
        isRequiredGeo = requiredGeo;
    }
}
