package ru.naumen.selenium.casesutil.model.mail;

import ru.naumen.selenium.casesutil.model.ModelUtils;

/**
 * Модель MS Graph сервера
 *
 * <AUTHOR>
 * @since 20 сент. 2024
 */
public class MsGraphServer extends MailServer
{
    private static final String FOLDERS = "folders";
    private static final String CORRECT_FOLDER = "correctFolders";
    private static final String INCORRECT_FOLDER = "incorrectFolders";

    public MsGraphServer()
    {
        super();
        model.put(PASSWORD, "*");
        model.put(PORT, "0");
        model.put(AUTH, Boolean.TRUE.toString());
        fillCredentials();
        model.put(CONNECTION_PROTOCOL, "MS Graph");
        params = new SendMailParam(getCode());
    }

    @Override
    public String getConnectionProtocol()
    {
        return "MS Graph";
    }

    @Override
    public String getProtocol()
    {
        return SecurityProtocol.UNSECURED.getCode();
    }

    @Override
    public String getPort()
    {
        return "0";
    }

    public String getApplicationId()
    {
        return get(APPLICATION_ID);
    }

    public String getClientId()
    {
        return get(CLIENT_ID);
    }

    public String getClientSecret()
    {
        return get(CLIENT_SECRET);
    }

    public void setApplicationId(String applicationId)
    {
        set(APPLICATION_ID, applicationId);
    }

    public void setClientId(String clientId)
    {
        set(CLIENT_ID, clientId);
    }

    public void setClientSecret(String clientSecret)
    {
        set(CLIENT_SECRET, clientSecret);
    }

    public void fillCredentials()
    {
        set(APPLICATION_ID, ModelUtils.createText(20));
        set(CLIENT_ID, ModelUtils.createText(20));
        set(CLIENT_SECRET, ModelUtils.createText(20));
    }

    public void setFolders(String folders)
    {
        set(FOLDERS, folders);
    }

    public void setCorrectFolder(String correctFolder)
    {
        set(CORRECT_FOLDER, correctFolder);
    }

    public void setIncorrectFolder(String incorrectFolder)
    {
        set(INCORRECT_FOLDER, incorrectFolder);
    }

    public String getFolders()
    {
        return get(FOLDERS);
    }

    public String getCorrectFolder()
    {
        return get(CORRECT_FOLDER);
    }

    public String getIncorrectFolder()
    {
        return get(INCORRECT_FOLDER);
    }
}