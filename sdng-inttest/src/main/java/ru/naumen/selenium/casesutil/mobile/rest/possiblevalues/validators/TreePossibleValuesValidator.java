package ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.nullValue;
import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.FROM_V15;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;

/**
 * Валидатор для возможных значений атрибута, представленных в виде дерева.
 *
 * <AUTHOR>
 * @since 01.08.2024
 */
public class TreePossibleValuesValidator extends BaseTreePossibleValuesValidator<TreePossibleValuesValidator>
{
    @Override
    public void validate(ValidatableResponse response, String absolutePath, MobileVersion version)
    {
        super.validate(response, absolutePath, version);
        if (!FROM_V15.contains(version))
        {
            response.body(absolutePath + ".foundAmount", is(0));
        }
        else
        {
            // чтобы исключить случайное использование проверки дерева вместо проверки результатов поиска по дереву
            response.body(absolutePath + ".foundAmount", is(nullValue()));
        }
    }
}
