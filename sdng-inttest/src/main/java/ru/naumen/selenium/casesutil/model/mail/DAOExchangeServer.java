package ru.naumen.selenium.casesutil.model.mail;

import ru.naumen.selenium.casesutil.mail.MailTestCase;
import ru.naumen.selenium.casesutil.model.ModelFactory;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.mail.MailServer.SecurityProtocol;
import ru.naumen.selenium.core.config.Config;

/**
 * Утилитарные методы для работы с моделями данных относящихся к Exchange серверу.
 * <b>Модель сервера, предназначенная для тестирования реальной отправки сообщений</b><br>
 * Для тестирования манипуляций с подключениями к серверам почты
 * со стороны SMP - необходимо использовать {@link DAOSmtpServer}.
 *
 * <AUTHOR>
 * @since 22.04.2021
 */
public class DAOExchangeServer
{
    /**
     * Создать модель Exchange сервера с настройками по-умолчанию
     * (Модель регистрируется в очередь на удаление)
     * @return возвращает модель Exchange сервера
     */
    public static ExchangeServer create()
    {
        ExchangeServer model = ModelFactory.create(ExchangeServer.class);
        model.setServer(ModelUtils.createText(10));
        model.setPort("0");
        model.setProtocol(SecurityProtocol.UNSECURED.getCode());
        model.setAuth(Boolean.TRUE.toString());
        model.fillCredentials();
        model.setLogin(ModelUtils.createLogin());
        model.setPassword("*");
        model.setDefault(Boolean.FALSE);
        model.setEnable(Boolean.FALSE.toString());
        model.setSkipCertVerification(Boolean.FALSE.toString());
        model.setParams(fillSendMailParams(new SendMailParam()));
        return model;
    }

    /**
     * Создать модель Exchange сервера с протоколом EWS и настройками по-умолчанию
     * (Модель регистрируется в очередь на удаление)
     * @return возвращает модель Exchange сервера с протоколом EWS
     */
    public static ExchangeServer createEWS()
    {
        ExchangeServer model = ModelFactory.create(ExchangeServer.class);
        model.setServer(Config.get().getExchangeServer());
        model.setPort("0");
        model.setConnectionProtocol("EWS");
        model.setAuth(Boolean.FALSE.toString());
        model.setApplicationId(Config.get().getExchangeApplicationId());
        model.setClientId(Config.get().getExchangeClientId());
        model.setClientSecret(Config.get().getExchangeClientSecret());
        model.setLogin(Config.get().getExchangeLogin());
        model.setPassword(Config.get().getExchangePassword());
        model.setDefault(Boolean.FALSE);
        model.setEnable(Boolean.TRUE.toString());
        model.setSkipCertVerification(Boolean.TRUE.toString());
        model.setFolders(MailTestCase.getBaseFolderPath());
        model.setCorrectFolder(MailTestCase.getOkFolderPath());
        model.setIncorrectFolder(MailTestCase.getErrorFolderPath());

        SendMailParam sendMailParam = new SendMailParam();
        sendMailParam.setFeedbackAddress(Config.get().getExchangeSharedMailBox());
        sendMailParam.setSystemMail(Config.get().getExchangeSharedMailBox());
        sendMailParam.setSenderName(ModelUtils.createTitle());
        model.setParams(sendMailParam);
        return model;
    }

    /**
     * Заполнить модель для настроек исходящей почты параметрами по-умолчанию
     * @return возвращает модель настроек исходящей почты
     */
    public static SendMailParam fillSendMailParams(SendMailParam params)
    {
        params.setFeedbackAddress(ModelUtils.createEmail());
        params.setSystemMail(ModelUtils.createEmail());
        params.setSenderName(ModelUtils.createTitle());
        return params;
    }
}
