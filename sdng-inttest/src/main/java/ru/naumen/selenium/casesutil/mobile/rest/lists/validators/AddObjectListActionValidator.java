package ru.naumen.selenium.casesutil.mobile.rest.lists.validators;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static ru.naumen.selenium.casesutil.mobile.rest.objects.MobileObjectPropertiesBuilder.ATTRIBUTE;
import static ru.naumen.selenium.casesutil.mobile.rest.objects.MobileObjectPropertiesBuilder.DELAYED_LINKING_INFO;
import static ru.naumen.selenium.casesutil.mobile.rest.objects.MobileObjectPropertiesBuilder.OBJECT;

import java.util.Map;

import org.junit.Assert;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.mobile.MobileAddForm;
import ru.naumen.selenium.util.Json;

/**
 * Валидатор для списочного действие типа "Добавить объект".
 *
 * <AUTHOR>
 * @since 22.11.2024
 */
public class AddObjectListActionValidator extends AbstractListActionValidator<AddObjectListActionValidator>
{
    private MobileAddForm form;
    private Map<String, Object> attributes = Map.of();
    private Bo object;
    private Attribute attribute;
    private final Map<String, Object> context = Map.of();

    AddObjectListActionValidator(String code, String title)
    {
        super(code, title);
    }

    /**
     * Позволяет валидировать код формы добавления
     */
    public AddObjectListActionValidator form(MobileAddForm form)
    {
        this.form = form;
        return this;
    }

    /**
     * Позволяет валидировать атрибуты для открытия формы добавления
     */
    public AddObjectListActionValidator attributes(Map<String, Object> attributes)
    {
        this.attributes = attributes;
        return this;
    }

    /**
     * Позволяет валидировать информацию об отложенном связывании создаваемого объекта
     *
     * @param object объект, с атрибутом которого объект должен быть связан после его создания
     * @param attribute ссылочный атрибут, с которым нужно объект будет связан после создания
     */
    public AddObjectListActionValidator delayedLinkingInfo(Bo object, Attribute attribute)
    {
        this.object = object;
        this.attribute = attribute;
        return this;
    }

    @Override
    public void validate(ValidatableResponse response, String absolutePath)
    {
        super.validate(response, absolutePath);

        response.body(absolutePath + "?.formCode", is(form.getCode()));
        response.body(absolutePath + "?.context", is(context));

        if (object != null)
        {
            response.body(absolutePath + "?.attributes?." + DELAYED_LINKING_INFO, notNullValue());
            String linkWith = response.extract().body().path(absolutePath + "?.attributes?." + DELAYED_LINKING_INFO);

            Map<String, Object> linkInfo = Json.GSON.fromJson(linkWith, Json.MAP_STRING_OBJECT);
            Assert.assertEquals(object.getUuid(), linkInfo.get(OBJECT));
            Assert.assertEquals(attribute.getCode(), linkInfo.get(ATTRIBUTE));
        }
        else
        {
            response.body(absolutePath + "?.attributes", is(attributes));
        }
    }
}
