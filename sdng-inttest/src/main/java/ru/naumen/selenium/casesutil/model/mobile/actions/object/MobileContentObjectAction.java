package ru.naumen.selenium.casesutil.model.mobile.actions.object;

import static ru.naumen.selenium.casesutil.bo.GUIButtonBar.BTN_FIRE_USER_EVENT;

import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.content.Tool.PresentationType;
import ru.naumen.selenium.casesutil.model.mobile.actions.MobileActionType;
import ru.naumen.selenium.casesutil.model.mobile.actions.MobileObjectAction;

/**
 * Действие над объектом в контенте "Параметры объекта"
 *
 * <AUTHOR>
 * @since 10.08.2022
 */
public class MobileContentObjectAction extends MobileObjectAction
{
    private final String backgroundColor;
    private final MobileActionsArea area;
    private final CatalogItem icon;
    private final PresentationType presentation;

    protected MobileContentObjectAction(MobileActionType type, String caption, MobileActionsArea area,
            PresentationType presentation, String backgroundColor, CatalogItem icon)
    {
        super(BTN_FIRE_USER_EVENT, type, caption);
        this.area = area;
        this.presentation = presentation;
        this.backgroundColor = backgroundColor;
        this.icon = icon;
    }

    public String getBackgroundColor()
    {
        return backgroundColor;
    }

    public MobileActionsArea getArea()
    {
        return area;
    }

    public CatalogItem getIcon()
    {
        return icon;
    }

    public PresentationType getPresentation()
    {
        return presentation;
    }
}