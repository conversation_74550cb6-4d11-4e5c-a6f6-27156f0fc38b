package ru.naumen.selenium.casesutil.model.mobile.actions.object;

/**
 * Область расположения действий над объектом в контенте "Параметры объекта"
 *
 * <AUTHOR>
 * @since 31.08.2022
 */
public enum MobileActionsArea
{
    /** Верхний блок действий */
    TOP_ACTION_BLOCK("topActionBlock", "TOP_ACTION_BLOCK", "Верхний блок действий"),
    /** Нижний блок действий */
    BOTTOM_ACTION_BLOCK("bottomActionBlock", "BOTTOM_ACTION_BLOCK", "Нижний блок действий");

    /** Код области расположения кнопки с действием на стороне мобильного клиента */
    private final String code;
    /** Id блоков контента с действиями в верстке форм добавления/редактирования действия */
    private final String formId;
    /** Названия блоков контента с действиями на формах добавления/редактирования действия */
    private final String formTitle;

    MobileActionsArea(String code, String formId, String formTitle)
    {
        this.code = code;
        this.formId = formId;
        this.formTitle = formTitle;
    }

    public String getCode()
    {
        return code;
    }

    public String getFormId()
    {
        return formId;
    }

    public String getFormTitle()
    {
        return formTitle;
    }
}